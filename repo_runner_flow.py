"""
Flow implementation for the Repo Runner system.
"""
import logging
from typing import Dict, Any
from pocketflow import Flow
from utils.conversation_logger import initialize_conversation_logger, get_conversation_logger

# Import nodes
from nodes import (
    RepositoryInitializationNode,
    FilePriorityAgentNode, 
    FileAnalysisNode,
    KnowledgeSynthesisNode
)
from enhancement_nodes import (
    GapDetectionNode,
    CodeAnalysisNode,
    AutoGenerationNode,
    ContainerStrategyNode,
    ValidationTestingNode,
    OptimizationNode
)


def create_repo_runner_flow() -> Flow:
    """
    Create the complete Repo Runner flow with all nodes and connections.
    
    Returns:
        Configured Flow object ready for execution
    """
    
    # Create all nodes
    repo_init = RepositoryInitializationNode()
    file_priority = FilePriorityAgentNode()
    file_analysis = FileAnalysisNode()
    knowledge_synthesis = KnowledgeSynthesisNode()
    gap_detection = GapDetectionNode()
    code_analysis = CodeAnalysisNode()
    auto_generation = AutoGenerationNode()
    container_strategy = ContainerStrategyNode()
    validation_testing = ValidationTestingNode()
    optimization = OptimizationNode()
    
    # Connect the main analysis loop
    repo_init >> file_priority
    file_priority - "continue_analysis" >> file_analysis
    file_analysis >> knowledge_synthesis
    
    # Knowledge synthesis decision points
    knowledge_synthesis - "need_more_info" >> file_priority
    knowledge_synthesis - "sufficient_info" >> gap_detection
    knowledge_synthesis - "max_iterations_reached" >> gap_detection
    
    # File priority agent decision points
    file_priority - "analysis_complete" >> gap_detection
    
    # Enhancement pipeline
    gap_detection - "gaps_analyzed" >> code_analysis
    gap_detection - "critical_gaps_found" >> code_analysis  # Continue even with critical gaps
    
    code_analysis >> auto_generation
    auto_generation >> container_strategy
    container_strategy >> validation_testing
    
    # Validation decision points
    validation_testing - "validation_passed" >> optimization
    validation_testing - "validation_failed" >> optimization  # Still optimize what we have
    
    # Create and return flow
    flow = Flow(start=repo_init)
    
    return flow


def initialize_shared_store(repository_url: str, access_token: str = None) -> Dict[str, Any]:
    """
    Initialize the shared store with default values.
    
    Args:
        repository_url: GitHub repository URL to analyze
        access_token: Optional GitHub access token
    
    Returns:
        Initialized shared store dictionary
    """
    return {
        "repository_url": repository_url,
        "access_token": access_token,
        "repository_metadata": {},
        "languages": {},
        "repo_path": "",
        "file_tree": [],
        "total_files": 0,
        "analyzed_files": {},
        "current_knowledge": {
            "language": None,
            "framework": None,
            "build_system": None,
            "dependencies": [],
            "entry_points": [],
            "configuration_files": [],
            "runtime_requirements": {},
            "detected_ports": [],
            "environment_variables": {},
            "database_requirements": [],
            "external_services": []
        },
        "files_to_analyze": [],
        "knowledge_gaps": {},
        "identified_gaps": {
            "missing_files": [],
            "missing_dependencies": [],
            "configuration_issues": [],
            "code_issues": [],
            "security_issues": [],
            "containerization_gaps": [],
            "critical_gaps": [],
            "severity": "low"
        },
        "code_issues": [],
        "generated_files": {},
        "code_modifications": {},
        "generation_suggestions": {},
        "container_strategy": {
            "dockerfile": "",
            "docker_compose": "",
            "build_instructions": "",
            "environment_setup": {},
            "port_mappings": {},
            "volume_mounts": [],
            "health_checks": {},
            "deployment_notes": [],
            "optimization_suggestions": [],
            "security_recommendations": []
        },
        "validation_results": {},
        "llm_validation": {},
        "validation_recommendations": [],
        "optimization_suggestions": [],
        "llm_optimizations": {},
        "completeness_assessment": {},
        "max_iterations": 50,
        "current_iteration": 0,
        "confidence_score": 0.0
    }


def run_repo_runner(repository_url: str, access_token: str = None, 
                   max_iterations: int = 50, log_level: str = "INFO") -> Dict[str, Any]:
    """
    Run the complete Repo Runner analysis on a repository.
    
    Args:
        repository_url: GitHub repository URL to analyze
        access_token: Optional GitHub access token for private repos
        max_iterations: Maximum number of file analysis iterations
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
    
    Returns:
        Complete analysis results including container strategy
    """
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Initialize conversation logger
    conversation_logger = initialize_conversation_logger()
    conversation_logger.log_repository_info(repository_url, bool(access_token))

    logger = logging.getLogger("RepoRunner")
    logger.info(f"Starting Repo Runner analysis for: {repository_url}")

    try:
        # Initialize shared store
        shared = initialize_shared_store(repository_url, access_token)
        shared["max_iterations"] = max_iterations

        # Create and run flow
        flow = create_repo_runner_flow()

        logger.info("Running Repo Runner flow...")
        flow.run(shared)

        # Extract results
        results = extract_results(shared)

        logger.info(f"Analysis complete! Confidence score: {results['confidence_score']:.2f}")
        logger.info(f"Generated {len(results['generated_files'])} files")
        logger.info(f"Container strategy status: {results['validation_status']}")

        # Finalize conversation logging
        conversation_logger.finalize_session(results)

        return results

    except Exception as e:
        logger.error(f"Repo Runner analysis failed: {e}")
        conversation_logger.log_error(f"Repo Runner analysis failed: {e}", exception=e)
        conversation_logger.finalize_session({"error": str(e), "success": False})
        raise
    
    finally:
        # Cleanup repository if it was cloned
        repo_path = shared.get("repo_path")
        if repo_path:
            try:
                from utils.github_client import cleanup_repository
                cleanup_repository(repo_path)
                logger.info("Repository cleanup completed")
            except Exception as e:
                logger.warning(f"Repository cleanup failed: {e}")


def extract_results(shared: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract and format the final results from the shared store.
    
    Args:
        shared: The shared store after flow execution
    
    Returns:
        Formatted results dictionary
    """
    
    return {
        # Repository information
        "repository_metadata": shared.get("repository_metadata", {}),
        "total_files_analyzed": len(shared.get("analyzed_files", {})),
        "analysis_iterations": shared.get("current_iteration", 0),
        
        # Knowledge extracted
        "current_knowledge": shared.get("current_knowledge", {}),
        "confidence_score": shared.get("confidence_score", 0.0),
        "completeness_assessment": shared.get("completeness_assessment", {}),
        
        # Gaps and issues identified
        "identified_gaps": shared.get("identified_gaps", {}),
        "code_issues": shared.get("code_issues", []),
        
        # Generated components
        "generated_files": shared.get("generated_files", {}),
        "code_modifications": shared.get("code_modifications", {}),
        
        # Container strategy
        "container_strategy": shared.get("container_strategy", {}),
        
        # Validation results
        "validation_results": shared.get("validation_results", {}),
        "validation_status": shared.get("validation_results", {}).get("overall_status", "unknown"),
        "validation_recommendations": shared.get("validation_recommendations", []),
        
        # Optimization suggestions
        "optimization_suggestions": shared.get("optimization_suggestions", []),
        "llm_optimizations": shared.get("llm_optimizations", {}),
        
        # Summary statistics
        "summary": {
            "language": shared.get("current_knowledge", {}).get("language"),
            "framework": shared.get("current_knowledge", {}).get("framework"),
            "dependencies_count": len(shared.get("current_knowledge", {}).get("dependencies", [])),
            "ports_detected": shared.get("current_knowledge", {}).get("detected_ports", []),
            "environment_variables_count": len(shared.get("current_knowledge", {}).get("environment_variables", {})),
            "database_requirements": shared.get("current_knowledge", {}).get("database_requirements", []),
            "files_generated": len(shared.get("generated_files", {})),
            "issues_found": len(shared.get("code_issues", [])),
            "critical_gaps": len(shared.get("identified_gaps", {}).get("critical_gaps", [])),
            "validation_passed": shared.get("validation_results", {}).get("overall_status") in ["passed", "partial"]
        }
    }


def print_results_summary(results: Dict[str, Any]):
    """
    Print a human-readable summary of the analysis results.
    
    Args:
        results: Results dictionary from extract_results()
    """
    
    print("\n" + "="*80)
    print("REPO RUNNER ANALYSIS RESULTS")
    print("="*80)
    
    # Repository info
    repo_name = results["repository_metadata"].get("full_name", "Unknown")
    print(f"\nRepository: {repo_name}")
    print(f"Files Analyzed: {results['total_files_analyzed']}")
    print(f"Analysis Iterations: {results['analysis_iterations']}")
    print(f"Confidence Score: {results['confidence_score']:.2f}")
    
    # Summary
    summary = results["summary"]
    print(f"\nDetected Language: {summary['language'] or 'Unknown'}")
    print(f"Detected Framework: {summary['framework'] or 'Unknown'}")
    print(f"Dependencies Found: {summary['dependencies_count']}")
    print(f"Ports Detected: {summary['ports_detected']}")
    print(f"Environment Variables: {summary['environment_variables_count']}")
    
    # Generated files
    print(f"\nFiles Generated: {summary['files_generated']}")
    if results["generated_files"]:
        for filename in results["generated_files"].keys():
            print(f"  - {filename}")
    
    # Issues and gaps
    print(f"\nIssues Found: {summary['issues_found']}")
    print(f"Critical Gaps: {summary['critical_gaps']}")
    
    # Validation
    validation_status = results["validation_status"]
    print(f"\nValidation Status: {validation_status.upper()}")
    print(f"Validation Passed: {'Yes' if summary['validation_passed'] else 'No'}")
    
    # Container strategy
    strategy = results["container_strategy"]
    print(f"\nContainer Strategy Generated: {'Yes' if strategy.get('dockerfile') else 'No'}")
    if strategy.get("dockerfile"):
        print(f"Dockerfile Length: {len(strategy['dockerfile'])} characters")
    if strategy.get("docker_compose"):
        print(f"Docker Compose: Generated")
    
    # Recommendations
    recommendations = results["validation_recommendations"]
    if recommendations:
        print(f"\nRecommendations ({len(recommendations)}):")
        for i, rec in enumerate(recommendations[:5], 1):  # Show first 5
            print(f"  {i}. {rec}")
        if len(recommendations) > 5:
            print(f"  ... and {len(recommendations) - 5} more")
    
    print("\n" + "="*80)


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python repo_runner_flow.py <repository_url> [access_token]")
        sys.exit(1)
    
    repository_url = sys.argv[1]
    access_token = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        results = run_repo_runner(repository_url, access_token, log_level="INFO")
        print_results_summary(results)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
