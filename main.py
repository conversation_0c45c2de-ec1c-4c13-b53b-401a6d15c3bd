"""
Main entry point for Repo Runner - Repository Analysis and Containerization Tool.
"""
import sys
import argparse
from repo_runner_flow import run_repo_runner, print_results_summary

def main():
    """Main function for Repo Runner."""
    parser = argparse.ArgumentParser(
        description="Repo Runner - Analyze repositories and generate containerization strategies"
    )

    parser.add_argument(
        "repository_url",
        help="GitHub repository URL to analyze (e.g., https://github.com/user/repo)"
    )

    parser.add_argument(
        "--token", "-t",
        help="GitHub access token for private repositories"
    )

    parser.add_argument(
        "--max-iterations", "-i",
        type=int,
        default=50,
        help="Maximum number of analysis iterations (default: 50)"
    )

    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )

    parser.add_argument(
        "--output", "-o",
        help="Output file for results (JSON format)"
    )

    parser.add_argument(
        "--generate-files", "-g",
        action="store_true",
        help="Generate containerization files to disk"
    )

    args = parser.parse_args()

    try:
        print(f"🚀 Starting Repo Runner analysis for: {args.repository_url}")

        # Run the analysis
        results = run_repo_runner(
            repository_url=args.repository_url,
            access_token=args.token,
            max_iterations=args.max_iterations,
            log_level=args.log_level
        )

        # Print results summary
        print_results_summary(results)

        # Generate files if requested
        if args.generate_files:
            from repo_runner_main import generate_files_to_disk
            generate_files_to_disk(results)

        # Save results if output file specified
        if args.output:
            from repo_runner_main import save_results_to_file
            save_results_to_file(results, args.output)
            print(f"📄 Results saved to: {args.output}")

        print("✅ Analysis completed successfully!")

    except KeyboardInterrupt:
        print("\n⏹️  Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
