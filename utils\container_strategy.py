"""
Container strategy generator for creating comprehensive containerization plans.
"""
import json
from typing import Dict, List, Any, Optional


def generate_container_strategy(repository_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate comprehensive container deployment strategy.
    
    Args:
        repository_analysis: Complete analysis of the repository
    
    Returns:
        Container strategy with Dockerfile, docker-compose, and deployment instructions
    """
    strategy = {
        'dockerfile': '',
        'docker_compose': '',
        'build_instructions': '',
        'environment_setup': {},
        'port_mappings': {},
        'volume_mounts': [],
        'health_checks': {},
        'deployment_notes': [],
        'optimization_suggestions': [],
        'security_recommendations': []
    }
    
    # Extract key information
    language = (repository_analysis.get('language') or '').lower()
    framework = (repository_analysis.get('framework') or '').lower()
    dependencies = repository_analysis.get('dependencies', [])
    ports = repository_analysis.get('detected_ports', [8080])
    env_vars = repository_analysis.get('environment_variables', {})
    db_connections = repository_analysis.get('database_requirements', [])
    entry_point = repository_analysis.get('entry_points', ['main'])[0] if repository_analysis.get('entry_points') else 'main'
    
    # Generate Dockerfile
    strategy['dockerfile'] = generate_optimized_dockerfile(
        language, framework, dependencies, ports[0] if ports else 8080, entry_point
    )
    
    # Generate docker-compose.yml
    strategy['docker_compose'] = generate_docker_compose(
        language, framework, ports, env_vars, db_connections
    )
    
    # Generate build instructions
    strategy['build_instructions'] = generate_build_instructions(language, framework)
    
    # Setup environment configuration
    strategy['environment_setup'] = generate_environment_setup(env_vars, db_connections)
    
    # Configure port mappings
    strategy['port_mappings'] = generate_port_mappings(ports)
    
    # Setup volume mounts
    strategy['volume_mounts'] = generate_volume_mounts(language, framework, db_connections)
    
    # Configure health checks
    strategy['health_checks'] = generate_health_checks(framework, ports[0] if ports else 8080)
    
    # Generate deployment notes
    strategy['deployment_notes'] = generate_deployment_notes(language, framework, db_connections)
    
    # Generate optimization suggestions
    strategy['optimization_suggestions'] = generate_optimization_suggestions(
        language, framework, repository_analysis
    )
    
    # Generate security recommendations
    strategy['security_recommendations'] = generate_security_recommendations(
        language, framework, env_vars
    )
    
    return strategy


def generate_optimized_dockerfile(language: str, framework: str, dependencies: List[str], 
                                port: int, entry_point: str) -> str:
    """Generate optimized Dockerfile with multi-stage builds and best practices."""
    
    if language == 'python':
        return generate_python_dockerfile(framework, port, entry_point)
    elif language in ['javascript', 'typescript']:
        return generate_node_dockerfile(framework, port, entry_point)
    elif language == 'go':
        return generate_go_dockerfile(port, entry_point)
    elif language == 'rust':
        return generate_rust_dockerfile(port, entry_point)
    elif language == 'java':
        return generate_java_dockerfile(framework, port, entry_point)
    else:
        return generate_generic_dockerfile(port)


def generate_python_dockerfile(framework: str, port: int, entry_point: str) -> str:
    """Generate optimized Python Dockerfile."""
    dockerfile = f"""# Multi-stage build for Python application
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder
COPY --from=builder /root/.local /root/.local

# Set working directory
WORKDIR /app

# Copy application code
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash appuser \\
    && chown -R appuser:appuser /app
USER appuser

# Make sure scripts in .local are usable
ENV PATH=/root/.local/bin:$PATH

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{port}/health || exit 1

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Run application
"""
    
    if framework == 'django':
        dockerfile += """CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]"""
    elif framework == 'flask':
        dockerfile += f"""CMD ["python", "{entry_point}"]"""
    elif framework == 'fastapi':
        app_module = entry_point.replace('.py', '') if entry_point.endswith('.py') else entry_point
        dockerfile += f"""CMD ["uvicorn", "{app_module}:app", "--host", "0.0.0.0", "--port", "{port}"]"""
    else:
        dockerfile += f"""CMD ["python", "{entry_point}"]"""
    
    return dockerfile


def generate_node_dockerfile(framework: str, port: int, entry_point: str) -> str:
    """Generate optimized Node.js Dockerfile."""
    dockerfile = f"""# Multi-stage build for Node.js application
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev)
RUN npm ci

# Copy source code
COPY . .

# Build application (if build script exists)
RUN npm run build --if-present

# Production stage
FROM node:18-alpine

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \\
    adduser -S nextjs -u 1001

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder --chown=nextjs:nodejs /app .

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD wget --no-verbose --tries=1 --spider http://localhost:{port}/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Run application
CMD ["node", "{entry_point}"]
"""
    
    return dockerfile


def generate_go_dockerfile(port: int, entry_point: str) -> str:
    """Generate optimized Go Dockerfile."""
    dockerfile = f"""# Multi-stage build for Go application
FROM golang:1.21-alpine AS builder

# Install git and ca-certificates
RUN apk add --no-cache git ca-certificates

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-extldflags "-static"' -o main .

# Final stage
FROM scratch

# Copy ca-certificates from builder
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the binary
COPY --from=builder /app/main /main

# Expose port
EXPOSE {port}

# Health check (using wget from alpine)
# Note: scratch doesn't have health check tools, so we'll use a simple approach
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
#     CMD /main --health-check || exit 1

# Run the binary
CMD ["/main"]
"""
    
    return dockerfile


def generate_rust_dockerfile(port: int, entry_point: str) -> str:
    """Generate optimized Rust Dockerfile."""
    dockerfile = f"""# Multi-stage build for Rust application
FROM rust:1.75 as builder

WORKDIR /app

# Copy manifests
COPY Cargo.toml Cargo.lock ./

# Create a dummy main.rs to build dependencies
RUN mkdir src && echo "fn main() {{}}" > src/main.rs

# Build dependencies (this will be cached)
RUN cargo build --release
RUN rm src/main.rs

# Copy source code
COPY src ./src

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    ca-certificates \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/target/release/* /app/

# Create non-root user
RUN useradd --create-home --shell /bin/bash appuser \\
    && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{port}/health || exit 1

# Run the binary
CMD ["./app"]
"""
    
    return dockerfile


def generate_java_dockerfile(framework: str, port: int, entry_point: str) -> str:
    """Generate optimized Java Dockerfile."""
    dockerfile = f"""# Multi-stage build for Java application
FROM openjdk:17-jdk-slim as builder

WORKDIR /app

# Copy build files
COPY pom.xml ./
COPY src ./src

# Build the application
RUN ./mvnw clean package -DskipTests

# Runtime stage
FROM openjdk:17-jre-slim

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy JAR from builder stage
COPY --from=builder /app/target/*.jar app.jar

# Create non-root user
RUN useradd --create-home --shell /bin/bash appuser \\
    && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{port}/health || exit 1

# JVM optimization
ENV JAVA_OPTS="-Xmx512m -Xms256m"

# Run application
CMD ["java", "-jar", "app.jar"]
"""
    
    return dockerfile


def generate_generic_dockerfile(port: int) -> str:
    """Generate generic Dockerfile."""
    return f"""# Generic Dockerfile - Please customize for your application
FROM ubuntu:22.04

# Install basic dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy application files
COPY . .

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{port}/health || exit 1

# TODO: Customize the CMD instruction for your application
CMD ["echo", "Please customize this Dockerfile for your specific application"]
"""


def generate_docker_compose(language: str, framework: str, ports: List[int], 
                          env_vars: Dict[str, Any], db_connections: List[Dict]) -> str:
    """Generate docker-compose.yml with all required services."""
    app_name = "app"
    main_port = ports[0] if ports else 8080
    
    compose = f"""version: '3.8'

services:
  {app_name}:
    build: .
    ports:
      - "{main_port}:{main_port}"
    environment:
"""
    
    # Add environment variables
    for env_var, default_value in env_vars.items():
        if default_value is not None:
            compose += f"      - {env_var}={default_value}\n"
        else:
            compose += f"      - {env_var}=${{env_var}}\n"
    
    # Add database services
    depends_on = []
    
    for db in db_connections:
        db_type = (db.get('type') or '').lower()
        
        if db_type == 'postgresql':
            depends_on.append('postgres')
            compose += f"""
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=app
      - POSTGRES_USER=app
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U app"]
      interval: 30s
      timeout: 10s
      retries: 5
"""
            compose = compose.replace("environment:", 
                                    "environment:\n      - DATABASE_URL=***************************************/app")
        
        elif db_type == 'mysql':
            depends_on.append('mysql')
            compose += f"""
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_DATABASE=app
      - MYSQL_USER=app
      - MYSQL_PASSWORD=password
      - MYSQL_ROOT_PASSWORD=rootpassword
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
"""
        
        elif db_type == 'redis':
            depends_on.append('redis')
            compose += f"""
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
"""
    
    # Add depends_on if there are dependencies
    if depends_on:
        compose = compose.replace("    environment:", 
                                f"    depends_on:\n" + 
                                "".join(f"      - {dep}\n" for dep in depends_on) +
                                "    environment:")
    
    # Add volumes section if needed
    if any(db.get('type') in ['postgresql', 'mysql'] for db in db_connections):
        compose += "\nvolumes:\n"
        if any(db.get('type') == 'postgresql' for db in db_connections):
            compose += "  postgres_data:\n"
        if any(db.get('type') == 'mysql' for db in db_connections):
            compose += "  mysql_data:\n"
    
    return compose


def generate_build_instructions(language: str, framework: str) -> str:
    """Generate build instructions."""
    instructions = f"""# Build Instructions

## Prerequisites
- Docker and Docker Compose installed
- Git (for cloning the repository)

## Quick Start
1. Clone the repository
2. Copy .env.example to .env and fill in your values
3. Run: `docker-compose up --build`

## Development
- Build image: `docker build -t {language}-app .`
- Run container: `docker run -p 8080:8080 {language}-app`
- Run with compose: `docker-compose up`

## Production
- Build for production: `docker build -t {language}-app:prod .`
- Use environment-specific compose files
- Consider using orchestration tools like Kubernetes

"""
    
    if language == 'python':
        instructions += """## Python-specific
- Virtual environment is handled inside the container
- Requirements are cached for faster builds
- Use multi-stage build for smaller production images
"""
    
    elif language in ['javascript', 'typescript']:
        instructions += """## Node.js-specific
- Dependencies are installed and cached
- Build step runs if package.json has build script
- Production dependencies only in final image
"""
    
    return instructions


def generate_environment_setup(env_vars: Dict[str, Any], db_connections: List[Dict]) -> Dict[str, Any]:
    """Generate environment setup configuration."""
    setup = {
        'required_variables': [],
        'optional_variables': [],
        'database_variables': [],
        'example_values': {}
    }
    
    for var, default_value in env_vars.items():
        if default_value is not None:
            setup['optional_variables'].append(var)
            setup['example_values'][var] = default_value
        else:
            setup['required_variables'].append(var)
    
    # Add database-specific variables
    for db in db_connections:
        db_type = (db.get('type') or '').lower()
        if db_type == 'postgresql':
            setup['database_variables'].append('DATABASE_URL')
            setup['example_values']['DATABASE_URL'] = 'postgresql://user:password@localhost:5432/dbname'
        elif db_type == 'mysql':
            setup['database_variables'].append('DATABASE_URL')
            setup['example_values']['DATABASE_URL'] = 'mysql://user:password@localhost:3306/dbname'
        elif db_type == 'redis':
            setup['database_variables'].append('REDIS_URL')
            setup['example_values']['REDIS_URL'] = 'redis://localhost:6379'
    
    return setup


def generate_port_mappings(ports: List[int]) -> Dict[str, Any]:
    """Generate port mapping configuration."""
    mappings = {
        'application_ports': ports,
        'recommended_mappings': {},
        'health_check_port': ports[0] if ports else 8080
    }
    
    for port in ports:
        mappings['recommended_mappings'][str(port)] = port
    
    return mappings


def generate_volume_mounts(language: str, framework: str, db_connections: List[Dict]) -> List[Dict[str, str]]:
    """Generate volume mount configuration."""
    mounts = []
    
    # Database volumes
    for db in db_connections:
        db_type = (db.get('type') or '').lower()
        if db_type == 'postgresql':
            mounts.append({
                'name': 'postgres_data',
                'mount_path': '/var/lib/postgresql/data',
                'description': 'PostgreSQL data persistence'
            })
        elif db_type == 'mysql':
            mounts.append({
                'name': 'mysql_data',
                'mount_path': '/var/lib/mysql',
                'description': 'MySQL data persistence'
            })
    
    # Application-specific volumes
    if language == 'python' and framework == 'django':
        mounts.append({
            'name': 'static_files',
            'mount_path': '/app/static',
            'description': 'Django static files'
        })
    
    return mounts


def generate_health_checks(framework: str, port: int) -> Dict[str, Any]:
    """Generate health check configuration."""
    health_checks = {
        'endpoint': '/health',
        'interval': '30s',
        'timeout': '10s',
        'retries': 3,
        'start_period': '5s'
    }
    
    # Framework-specific health check endpoints
    if framework == 'django':
        health_checks['endpoint'] = '/admin/'
        health_checks['description'] = 'Django admin endpoint for health check'
    elif framework == 'flask':
        health_checks['endpoint'] = '/health'
        health_checks['description'] = 'Flask health check endpoint'
    elif framework == 'express':
        health_checks['endpoint'] = '/health'
        health_checks['description'] = 'Express health check endpoint'
    elif framework == 'spring':
        health_checks['endpoint'] = '/actuator/health'
        health_checks['description'] = 'Spring Boot actuator health endpoint'
    
    health_checks['curl_command'] = f"curl -f http://localhost:{port}{health_checks['endpoint']}"
    
    return health_checks


def generate_deployment_notes(language: str, framework: str, db_connections: List[Dict]) -> List[str]:
    """Generate deployment notes and considerations."""
    notes = [
        "Review and customize environment variables before deployment",
        "Ensure all secrets are properly configured",
        "Test the application locally before deploying to production",
        "Consider using a reverse proxy (nginx) for production",
        "Set up proper logging and monitoring",
        "Configure backup strategies for persistent data"
    ]
    
    if db_connections:
        notes.append("Database services are included - ensure proper data persistence")
        notes.append("Consider using managed database services in production")
    
    if framework in ['django', 'flask']:
        notes.append("Consider using a WSGI server like Gunicorn for production")
    elif framework == 'express':
        notes.append("Consider using PM2 or similar process manager for production")
    
    return notes


def generate_optimization_suggestions(language: str, framework: str, analysis: Dict[str, Any]) -> List[str]:
    """Generate optimization suggestions."""
    suggestions = [
        "Use multi-stage builds to reduce image size",
        "Implement proper caching strategies",
        "Use .dockerignore to exclude unnecessary files",
        "Consider using Alpine Linux base images for smaller size",
        "Implement proper signal handling for graceful shutdowns"
    ]
    
    if language == 'python':
        suggestions.extend([
            "Use pip install --no-cache-dir to reduce image size",
            "Consider using Python slim images",
            "Pin dependency versions for reproducible builds"
        ])
    elif language in ['javascript', 'typescript']:
        suggestions.extend([
            "Use npm ci instead of npm install for faster, reliable builds",
            "Consider using node:alpine for smaller images",
            "Implement proper NODE_ENV handling"
        ])
    
    return suggestions


def generate_security_recommendations(language: str, framework: str, env_vars: Dict[str, Any]) -> List[str]:
    """Generate security recommendations."""
    recommendations = [
        "Run containers as non-root user",
        "Use specific image tags instead of 'latest'",
        "Regularly update base images and dependencies",
        "Scan images for vulnerabilities",
        "Use secrets management for sensitive data",
        "Implement proper network security policies"
    ]
    
    if env_vars:
        recommendations.extend([
            "Never hardcode secrets in images",
            "Use environment variables or secret management systems",
            "Validate and sanitize all environment inputs"
        ])
    
    if framework in ['django', 'flask', 'express']:
        recommendations.extend([
            "Enable security headers",
            "Use HTTPS in production",
            "Implement proper authentication and authorization"
        ])
    
    return recommendations


if __name__ == "__main__":
    # Test the container strategy generator
    test_analysis = {
        'language': 'python',
        'framework': 'flask',
        'dependencies': ['flask', 'requests', 'psycopg2'],
        'detected_ports': [5000],
        'environment_variables': {'DATABASE_URL': None, 'SECRET_KEY': 'dev-secret'},
        'database_requirements': [{'type': 'postgresql'}],
        'entry_points': ['app.py']
    }
    
    strategy = generate_container_strategy(test_analysis)
    
    print("Generated Container Strategy:")
    print("=" * 50)
    print("\nDockerfile:")
    print(strategy['dockerfile'][:500] + "...")
    print("\nDocker Compose:")
    print(strategy['docker_compose'][:500] + "...")
    print(f"\nOptimization Suggestions: {len(strategy['optimization_suggestions'])}")
    print(f"Security Recommendations: {len(strategy['security_recommendations'])}")
