#!/usr/bin/env python3
"""
Quick fix script to install python-dotenv and test .env file loading.
"""
import subprocess
import sys
import os


def install_python_dotenv():
    """Install python-dotenv package."""
    print("📦 Installing python-dotenv...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "python-dotenv"
        ], check=True)
        print("✅ python-dotenv installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install python-dotenv: {e}")
        return False


def test_env_loading():
    """Test loading .env file."""
    print("\n🧪 Testing .env file loading...")
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        print("❌ .env file not found in current directory")
        return False
    
    print("✅ .env file found")
    
    # Try to load it
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ dotenv loaded successfully")
        
        # Check if GEMINI_API_KEY is now available
        api_key = os.environ.get("GEMINI_API_KEY")
        if api_key:
            print(f"✅ GEMINI_API_KEY loaded: {api_key[:10]}...")
            return True
        else:
            print("❌ GEMINI_API_KEY not found in environment after loading .env")
            return False
    
    except ImportError:
        print("❌ python-dotenv not available")
        return False
    except Exception as e:
        print(f"❌ Error loading .env: {e}")
        return False


def show_env_file_content():
    """Show .env file content for debugging."""
    print("\n📄 .env file content:")
    try:
        with open(".env", "r") as f:
            content = f.read()
            print(content)
            
            # Check for common issues
            if "GEMINI_API_KEY=" not in content:
                print("⚠️  GEMINI_API_KEY not found in .env file")
            elif content.count("GEMINI_API_KEY=") > 1:
                print("⚠️  Multiple GEMINI_API_KEY entries found")
            else:
                print("✅ GEMINI_API_KEY found in .env file")
    
    except FileNotFoundError:
        print("❌ .env file not found")
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")


def test_gemini_api():
    """Test Gemini API with loaded environment."""
    print("\n🧠 Testing Gemini API...")
    
    try:
        # Import after loading environment
        from utils.call_llm import call_llm
        
        # Simple test
        response = call_llm("Say 'Hello from Gemini!' if you can read this.")
        if response and "hello" in response.lower():
            print("✅ Gemini API working!")
            print(f"Response: {response[:100]}...")
            return True
        else:
            print(f"⚠️  Unexpected response: {response}")
            return False
    
    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        return False


def main():
    """Main fix function."""
    print("🔧 Repo Runner .env Fix Script")
    print("=" * 40)
    
    # Install python-dotenv
    if not install_python_dotenv():
        print("\n❌ Cannot proceed without python-dotenv")
        return False
    
    # Show .env file content
    show_env_file_content()
    
    # Test loading
    if not test_env_loading():
        print("\n❌ .env loading failed")
        print("\nTroubleshooting:")
        print("1. Make sure .env file exists in current directory")
        print("2. Check that GEMINI_API_KEY=your-key is in the file")
        print("3. Make sure there are no extra spaces or quotes")
        print("4. Try running: python -c \"from dotenv import load_dotenv; load_dotenv(); import os; print(os.environ.get('GEMINI_API_KEY'))\"")
        return False
    
    # Test Gemini API
    if test_gemini_api():
        print("\n🎉 Everything is working!")
        print("\nYou can now run Repo Runner:")
        print("   python repo_runner_main.py https://github.com/user/repo")
        return True
    else:
        print("\n⚠️  .env loading works but Gemini API has issues")
        print("Check your API key at: https://makersuite.google.com/app/apikey")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
