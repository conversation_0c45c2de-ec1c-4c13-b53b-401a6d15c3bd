"""
Test script for the Repo Runner system.
"""
import os
import sys
import tempfile
import shutil
import json
from typing import Dict, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from repo_runner_flow import run_repo_runner, print_results_summary


def create_test_repository() -> str:
    """Create a simple test repository for testing."""
    
    # Create temporary directory
    test_repo = tempfile.mkdtemp(prefix="test_repo_")
    
    # Create a simple Flask application
    app_py_content = '''
import os
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello World!"

@app.route('/health')
def health():
    return "OK"

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'False').lower() == 'true'
    app.run(host='0.0.0.0', port=port, debug=debug)
'''
    
    # Create requirements.txt
    requirements_content = '''
Flask==2.3.0
gunicorn==20.1.0
'''
    
    # Create README.md
    readme_content = '''
# Test Flask Application

A simple Flask application for testing the Repo Runner.

## Running the Application

1. Install dependencies: `pip install -r requirements.txt`
2. Run the app: `python app.py`
3. Visit http://localhost:5000

## Environment Variables

- `PORT`: Port to run the application on (default: 5000)
- `DEBUG`: Enable debug mode (default: False)
'''
    
    # Create config.py
    config_content = '''
import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key')
    DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///app.db')
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
'''
    
    # Write files
    files = {
        'app.py': app_py_content,
        'requirements.txt': requirements_content,
        'README.md': readme_content,
        'config.py': config_content
    }
    
    for filename, content in files.items():
        with open(os.path.join(test_repo, filename), 'w') as f:
            f.write(content)
    
    return test_repo


def test_file_analyzer():
    """Test the file analyzer utility."""
    print("Testing file analyzer...")
    
    from utils.file_analyzer import analyze_file
    
    test_code = '''
import os
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello World!"

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port)
'''
    
    result = analyze_file('app.py', test_code)
    
    print(f"✓ File type: {result['file_type']}")
    print(f"✓ Language: {result['language']}")
    print(f"✓ Framework: {result.get('framework', 'None')}")
    print(f"✓ Ports: {result.get('ports', [])}")
    print(f"✓ Environment variables: {result.get('environment_variables', [])}")
    
    assert result['file_type'] == 'source_code'
    assert result['language'] == 'python'
    assert 'flask' in str(result).lower()
    
    print("File analyzer test passed!\n")


def test_gap_detector():
    """Test the gap detector utility."""
    print("Testing gap detector...")
    
    from utils.gap_detector import detect_gaps
    
    test_analysis = {
        'language': 'python',
        'framework': 'flask',
        'detected_ports': [5000],
        'environment_variables': ['PORT', 'DEBUG'],
        'dependencies': ['flask']
    }
    
    test_file_tree = [
        {'type': 'file', 'name': 'app.py', 'path': 'app.py'},
        {'type': 'file', 'name': 'README.md', 'path': 'README.md'}
    ]
    
    gaps = detect_gaps(test_analysis, test_file_tree)
    
    print(f"✓ Severity: {gaps['severity']}")
    print(f"✓ Missing files: {len(gaps['missing_files'])}")
    print(f"✓ Critical gaps: {len(gaps['critical_gaps'])}")
    
    # Should detect missing requirements.txt and Dockerfile
    missing_file_types = [gap['type'] for gap in gaps['missing_files']]
    assert 'dependency_file' in missing_file_types
    assert 'dockerfile' in missing_file_types
    
    print("Gap detector test passed!\n")


def test_auto_generator():
    """Test the auto generator utility."""
    print("Testing auto generator...")
    
    from utils.auto_generator import generate_missing_files
    
    test_missing = {
        'missing_files': [
            {'type': 'dependency_file', 'auto_generate': True},
            {'type': 'dockerfile', 'auto_generate': True}
        ]
    }
    
    test_context = {
        'language': 'python',
        'framework': 'flask',
        'dependencies': ['flask'],
        'detected_ports': [5000],
        'environment_variables': {'PORT': 5000, 'DEBUG': False},
        'repository_name': 'test-app'
    }
    
    generated = generate_missing_files(test_missing, test_context)
    
    print(f"✓ Generated files: {list(generated.keys())}")
    
    # Should generate requirements.txt and Dockerfile
    assert 'requirements.txt' in generated
    assert 'Dockerfile' in generated
    
    # Check content
    assert 'flask' in generated['requirements.txt'].lower()
    assert 'from python' in generated['Dockerfile'].lower()
    assert '5000' in generated['Dockerfile']
    
    print("Auto generator test passed!\n")


def test_priority_scorer():
    """Test the priority scorer utility."""
    print("Testing priority scorer...")
    
    from utils.priority_scorer import prioritize_files
    
    test_files = [
        {'type': 'file', 'name': 'package.json', 'path': 'package.json', 'size': 1500, 'depth': 0},
        {'type': 'file', 'name': 'app.py', 'path': 'app.py', 'size': 3000, 'depth': 0},
        {'type': 'file', 'name': 'utils.py', 'path': 'src/utils.py', 'size': 2000, 'depth': 1},
        {'type': 'file', 'name': 'README.md', 'path': 'README.md', 'size': 1200, 'depth': 0}
    ]
    
    test_knowledge = {
        'language': None,
        'framework': None,
        'dependencies': []
    }
    
    prioritized = prioritize_files(test_files, test_knowledge)
    
    print(f"✓ Prioritized {len(prioritized)} files")
    
    # package.json should have highest priority for language detection
    top_file = prioritized[0]
    print(f"✓ Top priority file: {top_file['name']} (score: {top_file['priority_score']:.1f})")
    
    assert top_file['name'] == 'package.json'
    assert top_file['priority_score'] > 50
    
    print("Priority scorer test passed!\n")


def test_container_strategy():
    """Test the container strategy generator."""
    print("Testing container strategy generator...")
    
    from utils.container_strategy import generate_container_strategy
    
    test_analysis = {
        'language': 'python',
        'framework': 'flask',
        'dependencies': ['flask', 'gunicorn'],
        'detected_ports': [5000],
        'environment_variables': {'PORT': 5000, 'DEBUG': False},
        'database_requirements': [],
        'entry_points': ['app.py']
    }
    
    strategy = generate_container_strategy(test_analysis)
    
    print(f"✓ Generated Dockerfile: {len(strategy['dockerfile'])} characters")
    print(f"✓ Generated docker-compose: {len(strategy['docker_compose'])} characters")
    print(f"✓ Build instructions: {len(strategy['build_instructions'])} characters")
    
    # Check Dockerfile content
    dockerfile = strategy['dockerfile']
    assert 'FROM python' in dockerfile
    assert 'COPY requirements.txt' in dockerfile
    assert 'EXPOSE 5000' in dockerfile
    assert 'CMD' in dockerfile
    
    print("Container strategy test passed!\n")


def test_full_workflow():
    """Test the complete workflow with a test repository."""
    print("Testing full workflow...")
    
    # Create test repository
    test_repo = create_test_repository()
    
    try:
        # Mock the repository URL and path
        from repo_runner_flow import initialize_shared_store, create_repo_runner_flow
        
        # Initialize shared store
        shared = initialize_shared_store("file://" + test_repo)
        shared["repo_path"] = test_repo
        shared["max_iterations"] = 10  # Limit for testing
        
        # Mock file tree
        shared["file_tree"] = [
            {'type': 'file', 'name': 'app.py', 'path': 'app.py', 'size': 500, 'depth': 0},
            {'type': 'file', 'name': 'requirements.txt', 'path': 'requirements.txt', 'size': 100, 'depth': 0},
            {'type': 'file', 'name': 'README.md', 'path': 'README.md', 'size': 300, 'depth': 0},
            {'type': 'file', 'name': 'config.py', 'path': 'config.py', 'size': 200, 'depth': 0}
        ]
        
        # Test individual nodes
        from nodes import FileAnalysisNode, KnowledgeSynthesisNode
        from enhancement_nodes import GapDetectionNode, AutoGenerationNode
        
        # Test file analysis
        file_analysis = FileAnalysisNode()
        shared["files_to_analyze"] = shared["file_tree"][:2]  # Analyze first 2 files
        
        prep_result = file_analysis.prep(shared)
        print(f"✓ Prepared {len(prep_result)} files for analysis")
        
        # Test knowledge synthesis
        knowledge_synthesis = KnowledgeSynthesisNode()
        
        # Mock some analyzed files
        shared["analyzed_files"] = {
            "app.py": {
                "language": "python",
                "framework": "flask",
                "ports": [5000],
                "environment_variables": ["PORT", "DEBUG"]
            }
        }
        
        prep_result = knowledge_synthesis.prep(shared)
        print(f"✓ Knowledge synthesis prepared")
        
        # Test gap detection
        gap_detection = GapDetectionNode()
        shared["current_knowledge"] = {
            "language": "python",
            "framework": "flask",
            "detected_ports": [5000],
            "environment_variables": {"PORT": 5000}
        }
        
        prep_result = gap_detection.prep(shared)
        print(f"✓ Gap detection prepared")
        
        print("Full workflow test passed!\n")
    
    finally:
        # Cleanup
        shutil.rmtree(test_repo)


def run_all_tests():
    """Run all tests."""
    print("Running Repo Runner Tests")
    print("=" * 50)

    # Check for required environment variables
    if not os.environ.get("GEMINI_API_KEY"):
        print("⚠️  Warning: GEMINI_API_KEY not set. LLM-dependent tests may fail.")
        print("   Set GEMINI_API_KEY environment variable to run full tests.")

    try:
        test_file_analyzer()
        test_gap_detector()
        test_auto_generator()
        test_priority_scorer()
        test_container_strategy()
        test_full_workflow()

        print("✅ All tests passed!")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
