"""
GitHub API client for repository analysis and cloning.
"""
import os
import subprocess
import tempfile
import shutil
from typing import Dict, List, Optional, Tuple
import requests
import json


def clone_repository(repository_url: str, target_dir: Optional[str] = None) -> str:
    """
    Clone a GitHub repository to local directory.
    
    Args:
        repository_url: GitHub repository URL
        target_dir: Target directory (if None, creates temp directory)
    
    Returns:
        Path to cloned repository
    """
    if target_dir is None:
        target_dir = tempfile.mkdtemp(prefix="repo_runner_")
    
    try:
        # Clean the URL and extract repo info
        if repository_url.endswith('.git'):
            repository_url = repository_url[:-4]
        
        # Clone the repository
        result = subprocess.run(
            ['git', 'clone', repository_url, target_dir],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode != 0:
            raise Exception(f"Git clone failed: {result.stderr}")
        
        return target_dir
    
    except subprocess.TimeoutExpired:
        raise Exception("Repository clone timed out")
    except Exception as e:
        if target_dir and os.path.exists(target_dir):
            shutil.rmtree(target_dir)
        raise e


def get_repository_metadata(repository_url: str, access_token: Optional[str] = None) -> Dict:
    """
    Get repository metadata from GitHub API.
    
    Args:
        repository_url: GitHub repository URL
        access_token: GitHub access token (optional)
    
    Returns:
        Repository metadata dictionary
    """
    # Extract owner and repo from URL
    parts = repository_url.replace('https://github.com/', '').replace('.git', '').split('/')
    if len(parts) < 2:
        raise ValueError("Invalid GitHub repository URL")
    
    owner, repo = parts[0], parts[1]
    
    # GitHub API endpoint
    api_url = f"https://api.github.com/repos/{owner}/{repo}"
    
    headers = {'Accept': 'application/vnd.github.v3+json'}
    if access_token:
        headers['Authorization'] = f'token {access_token}'
    
    try:
        response = requests.get(api_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        return {
            'name': data.get('name'),
            'full_name': data.get('full_name'),
            'description': data.get('description'),
            'language': data.get('language'),
            'languages_url': data.get('languages_url'),
            'size': data.get('size'),
            'stargazers_count': data.get('stargazers_count'),
            'forks_count': data.get('forks_count'),
            'topics': data.get('topics', []),
            'license': data.get('license', {}).get('name') if data.get('license') else None,
            'default_branch': data.get('default_branch'),
            'created_at': data.get('created_at'),
            'updated_at': data.get('updated_at'),
            'has_issues': data.get('has_issues'),
            'has_wiki': data.get('has_wiki'),
            'has_pages': data.get('has_pages'),
            'archived': data.get('archived'),
            'disabled': data.get('disabled'),
            'private': data.get('private')
        }
    
    except requests.exceptions.RequestException as e:
        raise Exception(f"Failed to fetch repository metadata: {e}")


def get_repository_languages(repository_url: str, access_token: Optional[str] = None) -> Dict[str, int]:
    """
    Get programming languages used in the repository.
    
    Args:
        repository_url: GitHub repository URL
        access_token: GitHub access token (optional)
    
    Returns:
        Dictionary of languages and their byte counts
    """
    # Extract owner and repo from URL
    parts = repository_url.replace('https://github.com/', '').replace('.git', '').split('/')
    owner, repo = parts[0], parts[1]
    
    api_url = f"https://api.github.com/repos/{owner}/{repo}/languages"
    
    headers = {'Accept': 'application/vnd.github.v3+json'}
    if access_token:
        headers['Authorization'] = f'token {access_token}'
    
    try:
        response = requests.get(api_url, headers=headers, timeout=30)
        response.raise_for_status()
        return response.json()
    
    except requests.exceptions.RequestException as e:
        print(f"Warning: Could not fetch languages: {e}")
        return {}


def build_file_tree(repo_path: str, max_depth: int = 10) -> List[Dict]:
    """
    Build a file tree structure of the repository.
    
    Args:
        repo_path: Path to cloned repository
        max_depth: Maximum directory depth to traverse
    
    Returns:
        List of file/directory information
    """
    file_tree = []
    
    def scan_directory(path: str, current_depth: int = 0):
        if current_depth > max_depth:
            return
        
        try:
            for item in os.listdir(path):
                if item.startswith('.git'):
                    continue
                
                item_path = os.path.join(path, item)
                relative_path = os.path.relpath(item_path, repo_path)
                
                if os.path.isfile(item_path):
                    try:
                        size = os.path.getsize(item_path)
                        file_info = {
                            'type': 'file',
                            'name': item,
                            'path': relative_path,
                            'size': size,
                            'extension': os.path.splitext(item)[1].lower(),
                            'depth': current_depth
                        }
                        file_tree.append(file_info)
                    except (OSError, IOError):
                        continue
                
                elif os.path.isdir(item_path):
                    dir_info = {
                        'type': 'directory',
                        'name': item,
                        'path': relative_path,
                        'depth': current_depth
                    }
                    file_tree.append(dir_info)
                    scan_directory(item_path, current_depth + 1)
        
        except (OSError, PermissionError):
            pass
    
    scan_directory(repo_path)
    return file_tree


def cleanup_repository(repo_path: str):
    """
    Clean up cloned repository directory.
    
    Args:
        repo_path: Path to repository directory to clean up
    """
    if os.path.exists(repo_path):
        try:
            shutil.rmtree(repo_path)
        except Exception as e:
            print(f"Warning: Could not clean up repository at {repo_path}: {e}")


if __name__ == "__main__":
    # Test the functions
    test_repo = "https://github.com/octocat/Hello-World"
    
    try:
        print("Testing repository metadata...")
        metadata = get_repository_metadata(test_repo)
        print(f"Repository: {metadata['full_name']}")
        print(f"Language: {metadata['language']}")
        print(f"Description: {metadata['description']}")
        
        print("\nTesting repository languages...")
        languages = get_repository_languages(test_repo)
        print(f"Languages: {languages}")
        
        print("\nTesting repository cloning...")
        repo_path = clone_repository(test_repo)
        print(f"Cloned to: {repo_path}")
        
        print("\nTesting file tree building...")
        file_tree = build_file_tree(repo_path)
        print(f"Found {len(file_tree)} items")
        for item in file_tree[:5]:  # Show first 5 items
            print(f"  {item['type']}: {item['path']}")
        
        print("\nCleaning up...")
        cleanup_repository(repo_path)
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
