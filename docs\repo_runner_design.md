# Design Doc: Repo Runner - Intelligent GitHub Repository Analyzer

> Please DON'T remove notes for AI

## Requirements

> Notes for AI: Keep it simple and clear.
> If the requirements are abstract, write concrete user stories

### Core Requirements

The Repo Runner is an intelligent system that analyzes GitHub repositories to understand their structure and determine how to run applications in containers. The system should:

1. **Repository Crawling**: <PERSON><PERSON> and systematically explore GitHub repositories
2. **Intelligent File Analysis**: Understand file contents and their relationships
3. **Container Configuration Discovery**: Identify how to run the application in a container
4. **Adaptive Learning**: Continue reading files until sufficient information is gathered
5. **Decision Making**: Intelligently decide which files to read next based on current understanding
6. **Gap Detection & Resolution**: Identify missing components and automatically generate them
7. **Code Analysis & Modification**: Detect and fix common containerization issues
8. **Environment Management**: Handle environment variables, secrets, and configuration
9. **Dependency Resolution**: Generate missing dependency files and resolve conflicts
10. **Port & Service Discovery**: Identify and configure correct ports and services

### User Stories

1. **As a DevOps engineer**, I want to automatically analyze a repository and get container deployment instructions without manual investigation, even if the repo is missing key files
2. **As a developer**, I want to understand how to run any GitHub project locally in a container with minimal setup, regardless of how incomplete the documentation is
3. **As a CI/CD system**, I want to automatically detect build and deployment configurations from repositories and generate missing pieces
4. **As a security auditor**, I want to understand the runtime requirements and dependencies of a repository, including auto-generated configurations
5. **As a platform engineer**, I want the system to fix common containerization issues like wrong ports, missing environment variables, or incorrect base images
6. **As a repository maintainer**, I want the system to suggest improvements and generate missing files to make my repository more container-friendly

## Flow Design

> Notes for AI:
> 1. Consider the design patterns of agent, map-reduce, rag, and workflow. Apply them if they fit.
> 2. Present a concise, high-level description of the workflow.

### Applicable Design Patterns:

1. **Agentic Pattern**: Intelligent decision-making agent that determines which files to read next and what actions to take
2. **Map-Reduce Pattern**: Map individual file analysis, reduce to overall understanding and gap identification
3. **RAG Pattern**: Retrieve relevant files and best practices based on current context, augment understanding
4. **Workflow Pattern**: Sequential steps with feedback loops for iterative learning and improvement
5. **Self-Healing Pattern**: Detect issues and automatically generate fixes or missing components

### Flow High-level Design:

1. **Repository Initialization Node**: Clone repository and perform initial scan
2. **File Priority Agent Node**: Intelligently prioritize which files to read next
3. **File Analysis Node**: Analyze selected files and extract relevant information
4. **Knowledge Synthesis Node**: Combine findings and assess completeness
5. **Gap Detection Node**: Identify missing files, configurations, and potential issues
6. **Code Analysis Node**: Analyze code for containerization issues (ports, paths, etc.)
7. **Auto-Generation Node**: Generate missing files and fix identified issues
8. **Container Strategy Node**: Generate comprehensive container deployment strategy
9. **Validation & Testing Node**: Verify the proposed solution and test container build
10. **Optimization Node**: Suggest improvements and optimizations

```mermaid
flowchart TD
    A[Repository Initialization] --> B[File Priority Agent]
    B --> C[File Analysis]
    C --> D[Knowledge Synthesis]
    D --> E{Sufficient Info?}
    E -->|No| B
    E -->|Yes| F[Gap Detection]
    F --> G[Code Analysis]
    G --> H[Auto-Generation]
    H --> I[Container Strategy]
    I --> J[Validation & Testing]
    J --> K{Issues Found?}
    K -->|Yes| L[Optimization]
    L --> B
    K -->|No| M[Output Results]

    F --> N{Critical Gaps?}
    N -->|Yes| B
    N -->|No| G
```

## Utility Functions

> Notes for AI:
> 1. Understand the utility function definition thoroughly by reviewing the doc.
> 2. Include only the necessary utility functions, based on nodes in the flow.

1. **Call LLM** (`utils/call_llm.py`)
   - *Input*: prompt (str)
   - *Output*: response (str)
   - Used by all analysis nodes for intelligent decision making

2. **GitHub API Client** (`utils/github_client.py`)
   - *Input*: repository_url (str), access_token (str)
   - *Output*: repository metadata and file tree
   - Used by Repository Initialization Node

3. **File Content Analyzer** (`utils/file_analyzer.py`)
   - *Input*: file_path (str), file_content (str)
   - *Output*: structured analysis (dict)
   - Used by File Analysis Node to extract metadata

4. **Container Strategy Generator** (`utils/container_strategy.py`)
   - *Input*: repository_analysis (dict)
   - *Output*: dockerfile and deployment instructions (dict)
   - Used by Container Strategy Node

5. **File Priority Scorer** (`utils/priority_scorer.py`)
   - *Input*: file_list (list), current_knowledge (dict)
   - *Output*: prioritized file list (list)
   - Used by File Priority Agent Node

6. **Gap Detector** (`utils/gap_detector.py`)
   - *Input*: repository_analysis (dict), file_tree (list)
   - *Output*: missing_components (dict), issues (list)
   - Used by Gap Detection Node

7. **Code Analyzer** (`utils/code_analyzer.py`)
   - *Input*: source_files (dict), language (str)
   - *Output*: code_issues (list), port_info (dict), dependencies (list)
   - Used by Code Analysis Node

8. **Auto Generator** (`utils/auto_generator.py`)
   - *Input*: missing_components (dict), template_context (dict)
   - *Output*: generated_files (dict)
   - Used by Auto-Generation Node

9. **Container Validator** (`utils/container_validator.py`)
   - *Input*: dockerfile (str), docker_compose (str)
   - *Output*: validation_results (dict), test_results (dict)
   - Used by Validation & Testing Node

## Node Design

### Shared Store

> Notes for AI: Try to minimize data redundancy

The shared store structure is organized as follows:

```python
shared = {
    "repository_url": "https://github.com/user/repo",
    "repository_metadata": {},
    "file_tree": [],
    "analyzed_files": {},
    "current_knowledge": {
        "language": None,
        "framework": None,
        "build_system": None,
        "dependencies": [],
        "entry_points": [],
        "configuration_files": [],
        "runtime_requirements": {},
        "detected_ports": [],
        "environment_variables": {},
        "database_requirements": [],
        "external_services": []
    },
    "files_to_analyze": [],
    "identified_gaps": {
        "missing_files": [],
        "missing_dependencies": [],
        "configuration_issues": [],
        "code_issues": []
    },
    "generated_files": {},
    "code_modifications": {},
    "container_strategy": {
        "dockerfile": "",
        "docker_compose": "",
        "build_instructions": "",
        "environment_setup": {},
        "port_mappings": {},
        "volume_mounts": [],
        "health_checks": {}
    },
    "validation_results": {},
    "optimization_suggestions": [],
    "max_iterations": 50,
    "current_iteration": 0,
    "confidence_score": 0.0
}
```

### Node Steps

> Notes for AI: Carefully decide whether to use Batch/Async Node/Flow.

1. **Repository Initialization Node**
   - *Purpose*: Clone repository, scan file structure, and identify high-priority files
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "repository_url" from shared store
     - *exec*: Clone repository, build file tree, identify critical files (README, package.json, Dockerfile, etc.)
     - *post*: Write "repository_metadata", "file_tree", and initial "files_to_analyze" to shared store

2. **File Priority Agent Node**
   - *Purpose*: Intelligently decide which files to read next based on current knowledge gaps
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "current_knowledge", "files_to_analyze", and "analyzed_files" from shared store
     - *exec*: Use LLM to analyze knowledge gaps and prioritize remaining files
     - *post*: Update "files_to_analyze" with prioritized list

3. **File Analysis Node**
   - *Purpose*: Analyze selected files and extract relevant information for containerization
   - *Type*: Batch Node (process multiple files efficiently)
   - *Steps*:
     - *prep*: Read top N files from "files_to_analyze" and their content
     - *exec*: Extract language, dependencies, build instructions, runtime requirements
     - *post*: Update "analyzed_files" and "current_knowledge" with findings

4. **Knowledge Synthesis Node**
   - *Purpose*: Combine all findings and determine if enough information exists for containerization
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "current_knowledge" and "analyzed_files" from shared store
     - *exec*: Use LLM to assess completeness and identify remaining gaps
     - *post*: Update "current_knowledge" with synthesis and set completion flag

5. **Container Strategy Node**
   - *Purpose*: Generate Dockerfile and deployment instructions based on gathered knowledge
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read complete "current_knowledge" from shared store
     - *exec*: Generate Dockerfile, docker-compose.yml, and deployment instructions
     - *post*: Write "container_strategy" to shared store

6. **Gap Detection Node**
   - *Purpose*: Identify missing files, configurations, and potential containerization issues
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "current_knowledge", "file_tree", and "analyzed_files" from shared store
     - *exec*: Analyze gaps in dependencies, configurations, and containerization requirements
     - *post*: Write "identified_gaps" to shared store and determine if critical gaps exist

7. **Code Analysis Node**
   - *Purpose*: Analyze source code for containerization-specific issues and requirements
   - *Type*: Batch Node (analyze multiple source files)
   - *Steps*:
     - *prep*: Read source files and current language/framework knowledge
     - *exec*: Extract ports, environment variables, file paths, database connections, external service calls
     - *post*: Update "current_knowledge" with discovered runtime requirements and potential issues

8. **Auto-Generation Node**
   - *Purpose*: Generate missing files and create code modifications to fix containerization issues
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read "identified_gaps" and "current_knowledge" from shared store
     - *exec*: Generate missing files (requirements.txt, Dockerfile, .env, etc.) and code fixes
     - *post*: Write "generated_files" and "code_modifications" to shared store

9. **Container Strategy Node**
   - *Purpose*: Generate comprehensive container deployment strategy with all components
   - *Type*: Regular Node
   - *Steps*:
     - *prep*: Read complete "current_knowledge", "generated_files", and "code_modifications"
     - *exec*: Generate Dockerfile, docker-compose.yml, build scripts, and deployment instructions
     - *post*: Write comprehensive "container_strategy" to shared store

10. **Validation & Testing Node**
    - *Purpose*: Validate the container strategy and test actual container build
    - *Type*: Regular Node
    - *Steps*:
      - *prep*: Read "container_strategy" and all generated components
      - *exec*: Validate configurations, test container build, check for runtime issues
      - *post*: Write "validation_results" and identify any remaining issues

11. **Optimization Node**
    - *Purpose*: Suggest improvements and optimizations for the container setup
    - *Type*: Regular Node
    - *Steps*:
      - *prep*: Read "container_strategy", "validation_results", and "current_knowledge"
      - *exec*: Analyze for security improvements, performance optimizations, best practices
      - *post*: Write "optimization_suggestions" and update confidence score

## Enhanced Intelligence Features

### Adaptive File Selection

The File Priority Agent uses contextual understanding to decide which files to read next:

- **Language Detection Priority**: package.json → Python requirements → Go modules → Rust Cargo.toml
- **Framework Detection**: Look for framework-specific files after language is identified
- **Build System Discovery**: Makefile, build scripts, CI/CD configurations
- **Configuration Files**: Environment files, config directories, deployment manifests
- **Issue-Driven Priority**: Prioritize files that might resolve identified gaps or issues

### Intelligent Gap Detection

The system identifies and addresses common missing components:

- **Missing Dependency Files**: Generate requirements.txt, package.json, go.mod, Cargo.toml
- **Missing Configuration**: Create .env templates, config files, logging configurations
- **Missing Build Files**: Generate Makefile, build scripts, CI/CD configurations
- **Missing Documentation**: Create README sections for containerization
- **Security Files**: Generate .dockerignore, security configurations

### Advanced Code Analysis

The system analyzes source code to extract containerization requirements:

- **Port Discovery**: Scan for server.listen(), app.run(), bind() calls to identify ports
- **Environment Variable Detection**: Find os.getenv(), process.env, config references
- **Database Connection Analysis**: Detect database drivers, connection strings, ORM configurations
- **File Path Analysis**: Identify hardcoded paths that need to be containerized
- **External Service Dependencies**: Find API calls, service integrations, third-party dependencies
- **Security Vulnerabilities**: Detect exposed secrets, insecure configurations

### Intelligent Code Modification

The system can modify code to make it container-friendly:

- **Port Configuration**: Replace hardcoded ports with environment variables
- **Path Normalization**: Convert absolute paths to relative or configurable paths
- **Environment Variable Injection**: Add environment variable support where missing
- **Configuration Externalization**: Move hardcoded configs to external files
- **Health Check Endpoints**: Add health check routes for container orchestration
- **Graceful Shutdown**: Implement proper signal handling for containers

### Smart Dependency Resolution

The system intelligently handles dependency management:

- **Version Conflict Resolution**: Detect and resolve dependency version conflicts
- **Missing Transitive Dependencies**: Identify and add missing indirect dependencies
- **Development vs Production**: Separate dev dependencies from production requirements
- **Security Updates**: Suggest security patches for vulnerable dependencies
- **Optimization**: Remove unused dependencies, suggest lighter alternatives

### Intelligent Stopping Criteria

The system knows when to stop crawling and proceed:

- **Sufficient Information**: All critical components identified for containerization
- **Diminishing Returns**: Recent files provide no new relevant information
- **Maximum Iterations**: Safety limit to prevent infinite crawling
- **Confidence Threshold**: High confidence in container strategy completeness
- **Gap Resolution**: All identified gaps have been addressed or marked as non-critical
- **Validation Success**: Container build and basic functionality tests pass

### Auto-Generation Capabilities

The system can generate comprehensive containerization assets:

**Generated Files:**
- `Dockerfile` with optimized multi-stage builds
- `docker-compose.yml` with all services and dependencies
- `requirements.txt` / `package.json` / language-specific dependency files
- `.dockerignore` with appropriate exclusions
- `.env.example` with all required environment variables
- `docker-entrypoint.sh` with proper initialization scripts
- Health check scripts and monitoring configurations
- CI/CD pipeline configurations for container deployment

**Code Modifications:**
- Configuration externalization (move hardcoded values to env vars)
- Port configuration flexibility
- Database connection string parameterization
- Logging configuration for containerized environments
- Signal handling for graceful shutdowns
- Path corrections for container filesystem layout

## Enhanced Error Handling and Edge Cases

### Repository Access Issues
- **Private repositories**: Handle authentication, suggest public alternatives
- **Large repositories**: Implement intelligent sampling, focus on critical paths
- **Monorepos**: Detect and handle multiple applications in single repository
- **Submodules**: Recursively analyze git submodules and dependencies
- **Non-standard structures**: Use heuristics and pattern matching for unusual layouts

### Analysis Challenges
- **Multi-language repositories**: Detect primary language, handle polyglot applications
- **Microservice architectures**: Generate separate containers for each service
- **Legacy codebases**: Apply modernization patterns, suggest gradual migration
- **Complex build processes**: Reverse-engineer build steps, create simplified alternatives
- **Missing documentation**: Generate documentation based on code analysis
- **Conflicting configurations**: Resolve conflicts using best practices and user preferences

### Code Issues and Fixes
- **Wrong ports**: Detect port mismatches, standardize on conventional ports
- **Hardcoded paths**: Replace with configurable paths and environment variables
- **Missing error handling**: Add basic error handling for container environments
- **Insecure configurations**: Fix security issues, add security best practices
- **Performance issues**: Suggest optimizations for containerized environments
- **Resource constraints**: Configure appropriate resource limits and requests

### Container Strategy Challenges
- **Applications requiring specific hardware**: Detect GPU/specialized hardware needs
- **Complex external dependencies**: Create docker-compose with all required services
- **Licensing restrictions**: Identify license conflicts, suggest alternatives
- **Database requirements**: Set up database containers with proper initialization
- **Caching layers**: Add Redis/Memcached containers when beneficial
- **Load balancing**: Configure nginx/traefik when multiple instances needed

### Validation and Testing Failures
- **Build failures**: Analyze errors, suggest fixes, retry with modifications
- **Runtime failures**: Add debugging, fix common startup issues
- **Port conflicts**: Automatically reassign ports, update configurations
- **Permission issues**: Fix file permissions, user configurations
- **Network connectivity**: Configure proper container networking
- **Volume mount issues**: Fix path mappings, create necessary directories

### Recovery Strategies
- **Graceful degradation**: Provide partial solutions when complete analysis fails
- **Alternative approaches**: Try different containerization strategies if first attempt fails
- **Manual intervention points**: Clearly identify where human input is needed
- **Rollback capabilities**: Maintain original files, allow reverting changes
- **Incremental improvement**: Allow iterative refinement of container strategy

## Intelligent Decision-Making Framework

### Priority Matrix for File Analysis
The system uses a sophisticated priority matrix to determine which files to analyze next:

**Critical Priority (Analyze First):**
- Package/dependency files (package.json, requirements.txt, go.mod, Cargo.toml)
- Main application entry points (main.py, app.js, main.go, src/main.rs)
- Configuration files (config.*, settings.*, .env)
- Dockerfile or existing container configurations

**High Priority:**
- Server/application startup files
- Database migration/schema files
- API route definitions
- Build scripts and Makefiles

**Medium Priority:**
- Test files (for understanding application behavior)
- Documentation files (README, docs/)
- CI/CD configuration files
- Static asset configurations

**Low Priority:**
- Individual source files (analyzed in batches)
- Generated files (node_modules/, target/, build/)
- Version control files (.git/, .gitignore)

### Gap Detection Algorithm
The system employs a comprehensive gap detection algorithm:

1. **Dependency Analysis**: Compare imported modules with declared dependencies
2. **Port Analysis**: Scan for server bindings, compare with exposed ports
3. **Environment Variable Audit**: Find env var usage, check for defaults
4. **File Path Validation**: Verify all referenced files exist or can be created
5. **Service Dependency Mapping**: Identify external services (databases, caches, APIs)
6. **Security Audit**: Check for exposed secrets, insecure configurations
7. **Performance Analysis**: Identify potential bottlenecks in containerized environment

### Auto-Generation Decision Tree
The system follows a decision tree for generating missing components:

```
Missing requirements.txt?
├── Yes: Analyze imports → Generate requirements.txt
└── No: Validate existing requirements

Wrong port configuration?
├── Yes: Extract port from code → Update configuration
└── No: Validate port accessibility

Missing environment variables?
├── Yes: Scan code for env usage → Generate .env.example
└── No: Validate existing env configuration

Database connections found?
├── Yes: Identify DB type → Add DB service to docker-compose
└── No: Continue analysis

Hardcoded paths detected?
├── Yes: Analyze path usage → Suggest code modifications
└── No: Continue analysis
```

### Confidence Scoring System
The system maintains a confidence score (0.0-1.0) based on:

- **Completeness**: Percentage of identified requirements addressed (40% weight)
- **Validation Success**: Container build and test results (30% weight)
- **Code Quality**: Absence of hardcoded values, proper configuration (20% weight)
- **Best Practices**: Following containerization best practices (10% weight)

**Confidence Thresholds:**
- 0.9+: High confidence, ready for production
- 0.7-0.9: Good confidence, minor optimizations suggested
- 0.5-0.7: Medium confidence, some issues need attention
- <0.5: Low confidence, significant issues detected

### Adaptive Learning Mechanism
The system learns and improves its analysis over time:

- **Pattern Recognition**: Learn common patterns across similar repositories
- **Success Rate Tracking**: Track which strategies work best for different project types
- **Error Pattern Analysis**: Learn from validation failures to improve future analysis
- **Best Practice Evolution**: Update recommendations based on industry best practices
- **User Feedback Integration**: Incorporate user corrections and preferences
