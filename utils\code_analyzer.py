"""
Code analyzer for extracting containerization requirements from source code.
"""
import re
import ast
import os
from typing import Dict, List, Set, Any, Optional
import json


def analyze_code_for_containerization(source_files: Dict[str, str], language: str) -> Dict[str, Any]:
    """
    Analyze source code files for containerization requirements.
    
    Args:
        source_files: Dictionary of file_path -> file_content
        language: Primary programming language
    
    Returns:
        Analysis results with ports, env vars, dependencies, etc.
    """
    analysis = {
        'ports': [],
        'environment_variables': {},
        'database_connections': [],
        'external_services': [],
        'file_paths': [],
        'security_issues': [],
        'configuration_patterns': [],
        'entry_points': [],
        'health_check_endpoints': [],
        'dependencies': [],
        'imports': []
    }
    
    for file_path, content in source_files.items():
        file_analysis = analyze_single_file(file_path, content, language)
        merge_analysis(analysis, file_analysis)
    
    # Post-process analysis
    analysis = post_process_analysis(analysis, language)
    
    return analysis


def analyze_single_file(file_path: str, content: str, language: str) -> Dict[str, Any]:
    """Analyze a single source file."""
    if language == 'python':
        return analyze_python_file(file_path, content)
    elif language in ['javascript', 'typescript']:
        return analyze_javascript_file(file_path, content)
    elif language == 'go':
        return analyze_go_file(file_path, content)
    elif language == 'java':
        return analyze_java_file(file_path, content)
    elif language == 'rust':
        return analyze_rust_file(file_path, content)
    else:
        return analyze_generic_file(file_path, content)


def analyze_python_file(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze Python source file."""
    analysis = {
        'ports': [],
        'environment_variables': {},
        'database_connections': [],
        'external_services': [],
        'file_paths': [],
        'security_issues': [],
        'entry_points': [],
        'imports': []
    }
    
    try:
        # Parse AST for more accurate analysis
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            # Find imports
            if isinstance(node, ast.Import):
                for alias in node.names:
                    analysis['imports'].append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    analysis['imports'].append(node.module)
            
            # Find function calls
            elif isinstance(node, ast.Call):
                if isinstance(node.func, ast.Attribute):
                    # app.run() calls
                    if (isinstance(node.func.value, ast.Name) and 
                        node.func.attr == 'run'):
                        for keyword in node.keywords:
                            if keyword.arg == 'port' and isinstance(keyword.value, ast.Constant):
                                analysis['ports'].append(keyword.value.value)
                    
                    # os.environ.get() calls
                    elif (isinstance(node.func.value, ast.Attribute) and
                          isinstance(node.func.value.value, ast.Name) and
                          node.func.value.value.id == 'os' and
                          node.func.value.attr == 'environ' and
                          node.func.attr == 'get'):
                        if node.args and isinstance(node.args[0], ast.Constant):
                            env_var = node.args[0].value
                            default_value = None
                            if len(node.args) > 1 and isinstance(node.args[1], ast.Constant):
                                default_value = node.args[1].value
                            analysis['environment_variables'][env_var] = default_value
    
    except SyntaxError:
        # Fall back to regex analysis if AST parsing fails
        pass
    
    # Regex-based analysis for additional patterns
    
    # Find Flask/Django/FastAPI patterns
    if 'flask' in content.lower():
        # Flask app.run() with port
        port_pattern = r'app\.run\([^)]*port\s*=\s*(\d+)'
        ports = re.findall(port_pattern, content)
        analysis['ports'].extend([int(p) for p in ports])
        
        # Flask routes (potential entry points)
        route_pattern = r'@app\.route\([\'"]([^\'"]+)[\'"]'
        routes = re.findall(route_pattern, content)
        analysis['entry_points'].extend(routes)
    
    if 'django' in content.lower():
        # Django URL patterns
        url_pattern = r'path\([\'"]([^\'"]+)[\'"]'
        urls = re.findall(url_pattern, content)
        analysis['entry_points'].extend(urls)
    
    # Database connection patterns
    db_patterns = {
        'postgresql': r'postgresql://[^\s\'"]+',
        'mysql': r'mysql://[^\s\'"]+',
        'mongodb': r'mongodb://[^\s\'"]+',
        'redis': r'redis://[^\s\'"]+',
        'sqlite': r'sqlite:///[^\s\'"]+',
    }
    
    for db_type, pattern in db_patterns.items():
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            analysis['database_connections'].append({
                'type': db_type,
                'connection_string': match
            })
    
    # File path patterns
    file_path_patterns = [
        r'[\'"](/[^\'"\s]+)[\'"]',  # Absolute paths
        r'open\([\'"]([^\'"\s]+)[\'"]',  # File operations
    ]
    
    for pattern in file_path_patterns:
        paths = re.findall(pattern, content)
        analysis['file_paths'].extend(paths)
    
    # Security issues
    if re.search(r'[\'"][a-zA-Z0-9]{20,}[\'"]', content):  # Potential API keys
        analysis['security_issues'].append('potential_hardcoded_secrets')
    
    return analysis


def analyze_javascript_file(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze JavaScript/TypeScript source file."""
    analysis = {
        'ports': [],
        'environment_variables': {},
        'database_connections': [],
        'external_services': [],
        'file_paths': [],
        'security_issues': [],
        'entry_points': []
    }
    
    # Express.js patterns
    if 'express' in content.lower():
        # app.listen() calls
        listen_pattern = r'\.listen\s*\(\s*(\d+)'
        ports = re.findall(listen_pattern, content)
        analysis['ports'].extend([int(p) for p in ports])
        
        # Express routes
        route_patterns = [
            r'app\.(get|post|put|delete|patch)\s*\([\'"]([^\'"]+)[\'"]',
            r'router\.(get|post|put|delete|patch)\s*\([\'"]([^\'"]+)[\'"]'
        ]
        
        for pattern in route_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                analysis['entry_points'].append(match[1])
    
    # Environment variables
    env_pattern = r'process\.env\.([A-Z_][A-Z0-9_]*)'
    env_vars = re.findall(env_pattern, content)
    for var in env_vars:
        analysis['environment_variables'][var] = None
    
    # Database connections
    db_patterns = {
        'mongodb': r'mongodb://[^\s\'"]+',
        'postgresql': r'postgresql://[^\s\'"]+',
        'mysql': r'mysql://[^\s\'"]+',
        'redis': r'redis://[^\s\'"]+',
    }
    
    for db_type, pattern in db_patterns.items():
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            analysis['database_connections'].append({
                'type': db_type,
                'connection_string': match
            })
    
    return analysis


def analyze_go_file(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze Go source file."""
    analysis = {
        'ports': [],
        'environment_variables': {},
        'database_connections': [],
        'external_services': [],
        'file_paths': [],
        'entry_points': []
    }
    
    # HTTP server patterns
    listen_patterns = [
        r'http\.ListenAndServe\s*\(\s*[\'"]:[^\'"]?(\d+)[\'"]',
        r'\.Addr\s*=\s*[\'"]:[^\'"]?(\d+)[\'"]'
    ]
    
    for pattern in listen_patterns:
        ports = re.findall(pattern, content)
        analysis['ports'].extend([int(p) for p in ports])
    
    # Environment variables
    env_pattern = r'os\.Getenv\s*\(\s*[\'"]([A-Z_][A-Z0-9_]*)[\'"]'
    env_vars = re.findall(env_pattern, content)
    for var in env_vars:
        analysis['environment_variables'][var] = None
    
    # HTTP handlers
    handler_pattern = r'http\.HandleFunc\s*\(\s*[\'"]([^\'"]+)[\'"]'
    handlers = re.findall(handler_pattern, content)
    analysis['entry_points'].extend(handlers)
    
    return analysis


def analyze_java_file(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze Java source file."""
    analysis = {
        'ports': [],
        'environment_variables': {},
        'database_connections': [],
        'external_services': [],
        'file_paths': [],
        'entry_points': []
    }
    
    # Spring Boot patterns
    if '@SpringBootApplication' in content or 'spring' in content.lower():
        # Server port configuration
        port_pattern = r'server\.port\s*=\s*(\d+)'
        ports = re.findall(port_pattern, content)
        analysis['ports'].extend([int(p) for p in ports])
        
        # REST endpoints
        endpoint_patterns = [
            r'@RequestMapping\s*\([^)]*value\s*=\s*[\'"]([^\'"]+)[\'"]',
            r'@GetMapping\s*\([\'"]([^\'"]+)[\'"]',
            r'@PostMapping\s*\([\'"]([^\'"]+)[\'"]'
        ]
        
        for pattern in endpoint_patterns:
            endpoints = re.findall(pattern, content)
            analysis['entry_points'].extend(endpoints)
    
    # Environment variables (System.getenv)
    env_pattern = r'System\.getenv\s*\(\s*[\'"]([A-Z_][A-Z0-9_]*)[\'"]'
    env_vars = re.findall(env_pattern, content)
    for var in env_vars:
        analysis['environment_variables'][var] = None
    
    return analysis


def analyze_rust_file(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze Rust source file."""
    analysis = {
        'ports': [],
        'environment_variables': {},
        'database_connections': [],
        'external_services': [],
        'file_paths': [],
        'entry_points': []
    }
    
    # HTTP server patterns (common Rust web frameworks)
    if 'actix' in content.lower() or 'warp' in content.lower() or 'rocket' in content.lower():
        # Port binding patterns
        port_patterns = [
            r'bind\s*\(\s*[\'"]127\.0\.0\.1:(\d+)[\'"]',
            r'bind\s*\(\s*[\'"]0\.0\.0\.0:(\d+)[\'"]',
            r'\.port\s*\(\s*(\d+)\s*\)'
        ]
        
        for pattern in port_patterns:
            ports = re.findall(pattern, content)
            analysis['ports'].extend([int(p) for p in ports])
    
    # Environment variables
    env_pattern = r'env::var\s*\(\s*[\'"]([A-Z_][A-Z0-9_]*)[\'"]'
    env_vars = re.findall(env_pattern, content)
    for var in env_vars:
        analysis['environment_variables'][var] = None
    
    return analysis


def analyze_generic_file(file_path: str, content: str) -> Dict[str, Any]:
    """Generic analysis for unknown languages."""
    analysis = {
        'ports': [],
        'environment_variables': {},
        'database_connections': [],
        'external_services': [],
        'file_paths': []
    }
    
    # Generic port patterns
    port_pattern = r'(?:port|PORT).*?(\d{4,5})'
    ports = re.findall(port_pattern, content)
    analysis['ports'].extend([int(p) for p in ports if 1000 <= int(p) <= 65535])
    
    # Generic environment variable patterns
    env_pattern = r'\$\{?([A-Z_][A-Z0-9_]*)\}?'
    env_vars = re.findall(env_pattern, content)
    for var in env_vars:
        analysis['environment_variables'][var] = None
    
    return analysis


def merge_analysis(main_analysis: Dict[str, Any], file_analysis: Dict[str, Any]):
    """Merge file analysis into main analysis."""
    for key, value in file_analysis.items():
        if key in main_analysis:
            if isinstance(value, list):
                main_analysis[key].extend(value)
            elif isinstance(value, dict):
                main_analysis[key].update(value)
            elif isinstance(value, set):
                main_analysis[key].update(value)


def post_process_analysis(analysis: Dict[str, Any], language: str) -> Dict[str, Any]:
    """Post-process analysis results."""
    # Remove duplicates
    analysis['ports'] = list(set(analysis['ports']))
    analysis['file_paths'] = list(set(analysis['file_paths']))
    analysis['entry_points'] = list(set(analysis['entry_points']))
    analysis['imports'] = list(set(analysis.get('imports', [])))
    
    # Identify main entry points
    if language == 'python':
        if 'app.py' in [os.path.basename(path) for path in analysis.get('file_paths', [])]:
            analysis['main_entry_point'] = 'app.py'
        elif 'main.py' in [os.path.basename(path) for path in analysis.get('file_paths', [])]:
            analysis['main_entry_point'] = 'main.py'
    
    elif language in ['javascript', 'typescript']:
        if 'index.js' in [os.path.basename(path) for path in analysis.get('file_paths', [])]:
            analysis['main_entry_point'] = 'index.js'
        elif 'server.js' in [os.path.basename(path) for path in analysis.get('file_paths', [])]:
            analysis['main_entry_point'] = 'server.js'
    
    # Detect common patterns
    if '/health' in analysis.get('entry_points', []):
        analysis['health_check_endpoints'] = ['/health']
    
    return analysis


if __name__ == "__main__":
    # Test the code analyzer
    test_code = '''
import os
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello World!"

@app.route('/health')
def health():
    return "OK"

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    db_url = os.environ.get('DATABASE_URL', 'sqlite:///app.db')
    app.run(host='0.0.0.0', port=port)
    '''
    
    result = analyze_code_for_containerization({'app.py': test_code}, 'python')
    print("Code analysis result:")
    print(json.dumps(result, indent=2))
