"""
Main entry point for the Repo Runner system.
"""
import argparse
import json
import os
import sys
import logging
from typing import Dict, Any, Optional

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Loaded environment variables from .env file")
except ImportError:
    print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")
except Exception as e:
    print(f"⚠️  Could not load .env file: {e}")

from repo_runner_flow import run_repo_runner, print_results_summary


def main():
    """Main entry point for the Repo Runner CLI."""
    
    parser = argparse.ArgumentParser(
        description="Repo Runner - Intelligent Repository Containerization Analysis (Powered by Google Gemini)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Set up Gemini API key first
  export GEMINI_API_KEY="your-gemini-api-key"

  # Analyze a public repository
  python repo_runner_main.py https://github.com/user/repo

  # Analyze with GitHub token for private repos
  python repo_runner_main.py https://github.com/user/repo --token YOUR_TOKEN

  # Save results to file
  python repo_runner_main.py https://github.com/user/repo --output results.json

  # Verbose logging
  python repo_runner_main.py https://github.com/user/repo --log-level DEBUG

  # Custom iteration limit
  python repo_runner_main.py https://github.com/user/repo --max-iterations 30

Environment Variables:
  GEMINI_API_KEY    Google Gemini API key (required)
  GITHUB_TOKEN      GitHub access token (optional, for private repos)
        """
    )
    
    parser.add_argument(
        "repository_url",
        help="GitHub repository URL to analyze"
    )
    
    parser.add_argument(
        "--token", "-t",
        help="GitHub access token for private repositories"
    )
    
    parser.add_argument(
        "--output", "-o",
        help="Output file path for results (JSON format)"
    )
    
    parser.add_argument(
        "--max-iterations", "-m",
        type=int,
        default=50,
        help="Maximum number of file analysis iterations (default: 50)"
    )
    
    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress summary output (useful when saving to file)"
    )
    
    parser.add_argument(
        "--generate-files",
        action="store_true",
        help="Actually write generated files to disk (in ./generated/ directory)"
    )
    
    parser.add_argument(
        "--validate-build",
        action="store_true",
        help="Attempt to validate by building Docker image (requires Docker)"
    )
    
    args = parser.parse_args()
    
    # Validate repository URL
    if not is_valid_github_url(args.repository_url):
        print("Error: Invalid GitHub repository URL")
        print("Expected format: https://github.com/owner/repo")
        sys.exit(1)
    
    # Check for required Gemini API key
    if not os.environ.get("GEMINI_API_KEY"):
        print("Error: GEMINI_API_KEY environment variable is required")
        print("Get your API key from: https://makersuite.google.com/app/apikey")
        print("Then set it with: export GEMINI_API_KEY='your-api-key'")
        sys.exit(1)

    # Get GitHub token from environment if not provided
    access_token = args.token or os.environ.get("GITHUB_TOKEN")

    try:
        # Run the analysis
        print(f"Starting Repo Runner analysis for: {args.repository_url}")
        if access_token:
            print("Using GitHub access token for authentication")
        
        results = run_repo_runner(
            repository_url=args.repository_url,
            access_token=access_token,
            max_iterations=args.max_iterations,
            log_level=args.log_level
        )
        
        # Save results to file if requested
        if args.output:
            save_results_to_file(results, args.output)
            print(f"Results saved to: {args.output}")
        
        # Generate files if requested
        if args.generate_files:
            generate_files_to_disk(results)
            print("Generated files written to ./generated/ directory")
        
        # Print summary unless quiet mode
        if not args.quiet:
            print_results_summary(results)
        
        # Print quick stats
        confidence = results["confidence_score"]
        validation_status = results["validation_status"]
        
        print(f"\nAnalysis completed successfully!")
        print(f"Confidence Score: {confidence:.2f}")
        print(f"Validation Status: {validation_status}")
        
        # Exit with appropriate code
        if validation_status in ["passed", "partial"]:
            sys.exit(0)
        else:
            print("Warning: Validation failed - review the generated configuration")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user")
        sys.exit(130)
    
    except Exception as e:
        print(f"Error: {e}")
        if args.log_level == "DEBUG":
            import traceback
            traceback.print_exc()
        sys.exit(1)


def is_valid_github_url(url: str) -> bool:
    """Validate GitHub repository URL format."""
    import re
    
    patterns = [
        r"^https://github\.com/[^/]+/[^/]+/?$",
        r"^https://github\.com/[^/]+/[^/]+\.git/?$",
        r"^git@github\.com:[^/]+/[^/]+\.git$"
    ]
    
    return any(re.match(pattern, url) for pattern in patterns)


def save_results_to_file(results: Dict[str, Any], output_path: str):
    """Save analysis results to JSON file."""
    try:
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Convert results to JSON-serializable format
        json_results = make_json_serializable(results)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
    
    except Exception as e:
        raise Exception(f"Failed to save results to {output_path}: {e}")


def make_json_serializable(obj: Any) -> Any:
    """Convert object to JSON-serializable format."""
    if isinstance(obj, dict):
        return {key: make_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, (str, int, float, bool, type(None))):
        return obj
    else:
        return str(obj)


def generate_files_to_disk(results: Dict[str, Any]):
    """Write generated files to disk in ./generated/ directory."""
    generated_files = results.get("generated_files", {})
    
    if not generated_files:
        print("No files were generated to write to disk")
        return
    
    output_dir = "./generated"
    os.makedirs(output_dir, exist_ok=True)
    
    for filename, content in generated_files.items():
        file_path = os.path.join(output_dir, filename)
        
        try:
            # Create subdirectories if needed
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"Generated: {file_path}")
        
        except Exception as e:
            print(f"Warning: Failed to write {filename}: {e}")


def interactive_mode():
    """Run Repo Runner in interactive mode."""
    print("Repo Runner - Interactive Mode")
    print("=" * 40)
    
    # Get repository URL
    while True:
        repo_url = input("Enter GitHub repository URL: ").strip()
        if is_valid_github_url(repo_url):
            break
        print("Invalid URL format. Please use: https://github.com/owner/repo")
    
    # Get optional token
    token = input("GitHub token (optional, press Enter to skip): ").strip()
    if not token:
        token = None
    
    # Get options
    print("\nOptions:")
    max_iterations = input("Max iterations (default 50): ").strip()
    max_iterations = int(max_iterations) if max_iterations.isdigit() else 50
    
    log_level = input("Log level (DEBUG/INFO/WARNING/ERROR, default INFO): ").strip().upper()
    if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR"]:
        log_level = "INFO"
    
    generate_files = input("Generate files to disk? (y/N): ").strip().lower() == 'y'
    
    try:
        print(f"\nStarting analysis of {repo_url}...")
        
        results = run_repo_runner(
            repository_url=repo_url,
            access_token=token,
            max_iterations=max_iterations,
            log_level=log_level
        )
        
        if generate_files:
            generate_files_to_disk(results)
        
        print_results_summary(results)
        
        # Ask if user wants to save results
        save_results = input("\nSave results to file? (y/N): ").strip().lower() == 'y'
        if save_results:
            output_file = input("Output filename (default: results.json): ").strip()
            if not output_file:
                output_file = "results.json"
            
            save_results_to_file(results, output_file)
            print(f"Results saved to {output_file}")
    
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True


if __name__ == "__main__":
    # Check if running in interactive mode
    if len(sys.argv) == 1:
        # No arguments provided, run interactive mode
        try:
            success = interactive_mode()
            sys.exit(0 if success else 1)
        except KeyboardInterrupt:
            print("\nGoodbye!")
            sys.exit(0)
    else:
        # Arguments provided, run CLI mode
        main()
