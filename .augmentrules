---
layout: default
title: "Agentic Coding"
---

# Agentic Coding: Humans Design, Agents code!

> If you are an AI agent involved in building LLM Systems, read this guide **VERY, VERY** carefully! This is the most important chapter in the entire document. Throughout development, you should always (1) start with a small and simple solution, (2) design at a high level (`docs/design.md`) before implementation, and (3) frequently ask humans for feedback and clarification.
{: .warning }

## Agentic Coding Steps

Agentic Coding should be a collaboration between Human System Design and Agent Implementation:

| Steps                  | Human      | AI        | Comment                                                                 |
|:-----------------------|:----------:|:---------:|:------------------------------------------------------------------------|
| 1. Requirements | ★★★ High  | ★☆☆ Low   | Humans understand the requirements and context.                    |
| 2. Flow          | ★★☆ Medium | ★★☆ Medium |  Humans specify the high-level design, and the AI fills in the details. |
| 3. Utilities   | ★★☆ Medium | ★★☆ Medium | Humans provide available external APIs and integrations, and the AI helps with implementation. |
| 4. Node          | ★☆☆ Low   | ★★★ High  | The AI helps design the node types and data handling based on the flow.          |
| 5. Implementation      | ★☆☆ Low   | ★★★ High  |  The AI implements the flow based on the design. |
| 6. Optimization        | ★★☆ Medium | ★★☆ Medium | Humans evaluate the results, and the AI helps optimize. |
| 7. Reliability         | ★☆☆ Low   | ★★★ High  |  The AI writes test cases and addresses corner cases.     |

1. **Requirements**: Clarify the requirements for your project, and evaluate whether an AI system is a good fit. 
    - Understand AI systems' strengths and limitations:
      - **Good for**: Routine tasks requiring common sense (filling forms, replying to emails)
      - **Good for**: Creative tasks with well-defined inputs (building slides, writing SQL)
      - **Not good for**: Ambiguous problems requiring complex decision-making (business strategy, startup planning)
    - **Keep It User-Centric:** Explain the "problem" from the user's perspective rather than just listing features.
    - **Balance complexity vs. impact**: Aim to deliver the highest value features with minimal complexity early.

2. **Flow Design**: Outline at a high level, describe how your AI system orchestrates nodes.
    - Identify applicable design patterns (e.g., Map Reduce, Agent, RAG).
      - For each node in the flow, start with a high-level one-line description of what it does.
      - If using **Map Reduce**, specify how to map (what to split) and how to reduce (how to combine).
      - If using **Agent**, specify what are the inputs (context) and what are the possible actions.
      - If using **RAG**, specify what to embed, noting that there's usually both offline (indexing) and online (retrieval) workflows.
    - Outline the flow and draw it in a mermaid diagram. For example:
      ```mermaid
      flowchart LR
          start[Start] --> batch[Batch]
          batch --> check[Check]
          check -->|OK| process
          check -->|Error| fix[Fix]
          fix --> check
          
          subgraph process[Process]
            step1[Step 1] --> step2[Step 2]
          end
          
          process --> endNode[End]
      ```
    - > **If Humans can't specify the flow, AI Agents can't automate it!** Before building an LLM system, thoroughly understand the problem and potential solution by manually solving example inputs to develop intuition.  
      {: .best-practice }

3. **Utilities**: Based on the Flow Design, identify and implement necessary utility functions.
    - Think of your AI system as the brain. It needs a body—these *external utility functions*—to interact with the real world:
        - Reading inputs (e.g., retrieving Slack messages, reading emails)
        - Writing outputs (e.g., generating reports, sending emails)
        - Using external tools (e.g., calling LLMs, searching the web)
        - **NOTE**: *LLM-based tasks* (e.g., summarizing text, analyzing sentiment) are **NOT** utility functions; rather, they are *core functions* internal in the AI system.
    - For each utility function, implement it and write a simple test.
    - Document their input/output, as well as why they are necessary. For example:
      - `name`: `get_embedding` (`utils/get_embedding.py`)
      - `input`: `str`
      - `output`: a vector of 3072 floats
      - `necessity`: Used by the second node to embed text
    - Example utility implementation:
      ```python
      # utils/call_llm.py
      from openai import OpenAI

      def call_llm(prompt):    
          client = OpenAI(api_key="YOUR_API_KEY_HERE")
          r = client.chat.completions.create(
              model="gpt-4o",
              messages=[{"role": "user", "content": prompt}]
          )
          return r.choices[0].message.content
          
      if __name__ == "__main__":
          prompt = "What is the meaning of life?"
          print(call_llm(prompt))
      ```
    - > **Sometimes, design Utilities before Flow:**  For example, for an LLM project to automate a legacy system, the bottleneck will likely be the available interface to that system. Start by designing the hardest utilities for interfacing, and then build the flow around them.
      {: .best-practice }

4. **Node Design**: Plan how each node will read and write data, and use utility functions.
   - One core design principle for PocketFlow is to use a shared store, so start with a shared store design:
      - For simple systems, use an in-memory dictionary.
      - For more complex systems or when persistence is required, use a database.
      - **Don't Repeat Yourself**: Use in-memory references or foreign keys.
      - Example shared store design:
        ```python
        shared = {
            "user": {
                "id": "user123",
                "context": {                # Another nested dict
                    "weather": {"temp": 72, "condition": "sunny"},
                    "location": "San Francisco"
                }
            },
            "results": {}                   # Empty dict to store outputs
        }
        ```
   - For each Node, describe its type, how it reads and writes data, and which utility function it uses. Keep it specific but high-level without codes. For example:
     - `type`: Regular (or Batch, or Async)
     - `prep`: Read "text" from the shared store
     - `exec`: Call the embedding utility function
     - `post`: Write "embedding" to the shared store

5. **Implementation**: Implement the initial nodes and flows based on the design.
   - 🎉 If you've reached this step, humans have finished the design. Now *Agentic Coding* begins!
   - **"Keep it simple, stupid!"** Avoid complex features and full-scale type checking.
   - **FAIL FAST**! Avoid `try` logic so you can quickly identify any weak points in the system.
   - Add logging throughout the code to facilitate debugging.

7. **Optimization**:
   - **Use Intuition**: For a quick initial evaluation, human intuition is often a good start.
   - **Redesign Flow (Back to Step 3)**: Consider breaking down tasks further, introducing agentic decisions, or better managing input contexts.
   - If your flow design is already solid, move on to micro-optimizations:
     - **Prompt Engineering**: Use clear, specific instructions with examples to reduce ambiguity.
     - **In-Context Learning**: Provide robust examples for tasks that are difficult to specify with instructions alone.

   - > **You'll likely iterate a lot!** Expect to repeat Steps 3–6 hundreds of times.
     {: .best-practice }

8. **Reliability**  
   - **Node Retries**: Add checks in the node `exec` to ensure outputs meet requirements, and consider increasing `max_retries` and `wait` times.
   - **Logging and Visualization**: Maintain logs of all attempts and visualize node results for easier debugging.
   - **Self-Evaluation**: Add a separate node (powered by an LLM) to review outputs when results are uncertain.

## Example LLM Project File Structure

```
my_project/
├── main.py
├── nodes.py
├── flow.py
├── utils/
│   ├── __init__.py
│   ├── call_llm.py
│   └── search_web.py
├── requirements.txt
└── docs/
    └── design.md
```

- **`docs/design.md`**: Contains project documentation for each step above. This should be *high-level* and *no-code*.
- **`utils/`**: Contains all utility functions.
  - It's recommended to dedicate one Python file to each API call, for example `call_llm.py` or `search_web.py`.
  - Each file should also include a `main()` function to try that API call
- **`nodes.py`**: Contains all the node definitions.
- **`flow.py`**: Implements functions that create flows by importing node definitions and connecting them.
- **`main.py`**: Serves as the project's entry point.

## Augment-Specific Guidelines

### Task Management
When working on complex projects, use Augment's task management tools:
- Use `add_tasks` to break down work into meaningful units (20-minute chunks)
- Use `update_tasks` to track progress and mark completion
- Use batch updates when transitioning between tasks
- Update task states: NOT_STARTED [ ], IN_PROGRESS [/], CANCELLED [-], COMPLETE [x]

### Code Analysis and Editing
- Always use `codebase-retrieval` before making edits to understand existing code
- Use `str-replace-editor` for modifications, never overwrite entire files
- Ask for detailed information about symbols, classes, and methods involved in edits
- Be conservative and respect the existing codebase structure

### Package Management
- Always use appropriate package managers (npm, pip, cargo, etc.) instead of manually editing package files
- Only edit package configuration files directly for complex configurations that can't be done via package managers

### Testing and Validation
- Suggest writing tests after implementing code changes
- Run tests to validate implementations
- Use `diagnostics` to check for issues in the codebase

### Information Gathering
- Use `view` tool to examine files and directories
- Use `codebase-retrieval` for understanding code context
- Use `web-search` and `web-fetch` for external information when needed

### Communication
- Wrap code excerpts in `<augment_code_snippet>` tags with path and mode attributes
- Ask for clarification when requirements are unclear
- Provide progress updates when working on complex tasks
- Ask before performing potentially destructive actions

### Memory and Context
- Use `remember` tool for long-term information that will be useful across sessions
- Maintain context about the project structure and goals
- Reference previous work and decisions when relevant

================================================
File: docs/index.md
================================================

# Pocket Flow

A 100-line minimalist LLM framework for *Agents, Task Decomposition, RAG, etc*.

- **Lightweight**: Just the core graph abstraction in 100 lines. ZERO dependencies, and vendor lock-in.
- **Expressive**: Everything you love from larger frameworks—(Multi-)Agents, Workflow, RAG, and more.
- **Agentic-Coding**: Intuitive enough for AI agents to help humans build complex LLM applications.

## Core Abstraction

We model the LLM workflow as a **Graph + Shared Store**:

- Node handles simple (LLM) tasks.
- Flow connects nodes through **Actions** (labeled edges).
- Shared Store enables communication between nodes within flows.
- Batch nodes/flows allow for data-intensive tasks.
- Async nodes/flows allow waiting for asynchronous tasks.
- (Advanced) Parallel nodes/flows handle I/O-bound tasks.

## Design Pattern

From there, it's easy to implement popular design patterns:

- Agent autonomously makes decisions.
- Workflow chains multiple tasks into pipelines.
- RAG integrates data retrieval with generation.
- Map Reduce splits data tasks into Map and Reduce steps.
- Structured Output formats outputs consistently.
- (Advanced) Multi-Agents coordinate multiple agents.

## Utility Function

We **do not** provide built-in utilities. Instead, we offer *examples*—please *implement your own*:

- LLM Wrapper
- Viz and Debug
- Web Search
- Chunking
- Embedding
- Vector Databases
- Text-to-Speech

**Why not built-in?**: I believe it's a *bad practice* for vendor-specific APIs in a general framework:
- *API Volatility*: Frequent changes lead to heavy maintenance for hardcoded APIs.
- *Flexibility*: You may want to switch vendors, use fine-tuned models, or run them locally.
- *Optimizations*: Prompt caching, batching, and streaming are easier without vendor lock-in.

================================================
File: docs/core_abstraction/node.md
================================================

# Node

A **Node** is the basic unit of computation in PocketFlow. It represents a single step in your workflow.

## Basic Structure

Every node has three main methods:

```python
class MyNode(Node):
    def prep(self, shared):
        # Read data from shared store
        return data_for_exec

    def exec(self, prep_result):
        # Process the data (main logic)
        return processed_result

    def post(self, shared, prep_res, exec_res):
        # Write results back to shared store
        # Return action for next node
        return "default"
```

## Node Types

### Regular Node
Standard synchronous processing:

```python
class SummarizeNode(Node):
    def prep(self, shared):
        return shared["document"]

    def exec(self, document):
        return call_llm(f"Summarize: {document}")

    def post(self, shared, prep_res, exec_res):
        shared["summary"] = exec_res
        return "default"
```

### Batch Node
Process multiple items efficiently:

```python
class BatchSummarizeNode(BatchNode):
    def prep(self, shared):
        # Return iterable of items to process
        return shared["documents"]

    def exec(self, document):
        # Called once per document
        return call_llm(f"Summarize: {document}")

    def post(self, shared, prep_res, exec_res_list):
        # exec_res_list contains all results
        shared["summaries"] = exec_res_list
        return "default"
```

### Async Node
Handle asynchronous operations:

```python
class AsyncSummarizeNode(AsyncNode):
    async def prep_async(self, shared):
        return shared["document"]

    async def exec_async(self, document):
        return await call_llm_async(f"Summarize: {document}")

    async def post_async(self, shared, prep_res, exec_res):
        shared["summary"] = exec_res
        return "default"
```

## Error Handling and Retries

Nodes support automatic retries with exponential backoff:

```python
class ReliableNode(Node):
    def __init__(self, max_retries=3, wait_time=1.0):
        super().__init__(max_retries=max_retries, wait_time=wait_time)

    def exec(self, data):
        # This will be retried if it fails
        result = potentially_failing_operation(data)

        # Validate result
        if not self.is_valid_result(result):
            raise ValueError("Invalid result")

        return result

    def is_valid_result(self, result):
        # Custom validation logic
        return result is not None and len(result) > 0
```

================================================
File: docs/core_abstraction/flow.md
================================================

# Flow

A **Flow** connects multiple nodes together to create a workflow. Flows define the execution order and data flow between nodes.

## Basic Flow Creation

```python
from pocketflow import Flow

# Create nodes
node1 = FirstNode()
node2 = SecondNode()
node3 = ThirdNode()

# Connect nodes
node1 >> node2  # Default connection
node2 >> node3

# Create flow
flow = Flow(start=node1)

# Run the flow
shared = {"input": "data"}
flow.run(shared)
```

## Conditional Flows

Use actions to create conditional branching:

```python
class DecisionNode(Node):
    def exec(self, data):
        if data > 10:
            return "high"
        else:
            return "low"

    def post(self, shared, prep_res, exec_res):
        shared["decision"] = exec_res
        return exec_res  # Return action based on result

# Connect with conditions
decision_node = DecisionNode()
high_node = HighValueNode()
low_node = LowValueNode()

decision_node - "high" >> high_node
decision_node - "low" >> low_node

flow = Flow(start=decision_node)
```

## Flow Types

### Regular Flow
Standard synchronous execution:

```python
flow = Flow(start=first_node)
shared = {"data": "input"}
flow.run(shared)
```

### Async Flow
Handle asynchronous nodes:

```python
async_flow = AsyncFlow(start=async_node)
shared = {"data": "input"}
await async_flow.run_async(shared)
```

### Batch Flow
Process multiple parameter sets:

```python
class MultiBatchFlow(BatchFlow):
    def prep(self, shared):
        # Return list of parameter dicts
        return [{"id": 1}, {"id": 2}, {"id": 3}]

batch_flow = MultiBatchFlow(start=processing_node)
batch_flow.run(shared)

================================================
File: docs/design_pattern/agent.md
================================================

# Agent

An **Agent** is a design pattern where a node autonomously makes decisions about what action to take next based on the current context.

## Basic Agent Pattern

```python
class AgentNode(Node):
    def prep(self, shared):
        # Gather context for decision making
        context = {
            "current_state": shared.get("state"),
            "available_actions": ["search", "analyze", "report"],
            "history": shared.get("history", [])
        }
        return context

    def exec(self, context):
        # Use LLM to decide next action
        prompt = f"""
        Given the current context: {context}
        What should be the next action? Choose from: {context['available_actions']}
        Respond with just the action name.
        """
        decision = call_llm(prompt)
        return decision.strip().lower()

    def post(self, shared, prep_res, exec_res):
        # Update history and return action
        if "history" not in shared:
            shared["history"] = []
        shared["history"].append(exec_res)
        return exec_res
```

## Multi-Step Agent

```python
class PlanningAgent(Node):
    def exec(self, context):
        # Create a plan with multiple steps
        prompt = f"""
        Given this context: {context}
        Create a step-by-step plan to solve the problem.
        Return as JSON: {{"steps": ["step1", "step2", ...]}}
        """
        plan = call_llm(prompt)
        return json.loads(plan)

    def post(self, shared, prep_res, exec_res):
        shared["plan"] = exec_res["steps"]
        shared["current_step"] = 0
        return "execute_plan"

class ExecutionAgent(Node):
    def prep(self, shared):
        current_step = shared["current_step"]
        return shared["plan"][current_step]

    def exec(self, step):
        # Execute the current step
        result = execute_step(step)
        return result

    def post(self, shared, prep_res, exec_res):
        shared["current_step"] += 1
        shared["step_results"] = shared.get("step_results", [])
        shared["step_results"].append(exec_res)

        if shared["current_step"] >= len(shared["plan"]):
            return "complete"
        else:
            return "continue"
```

================================================
File: docs/design_pattern/rag.md
================================================

# RAG (Retrieval-Augmented Generation)

RAG combines information retrieval with text generation to provide more accurate and contextual responses.

## Basic RAG Pattern

```python
class RetrievalNode(Node):
    def prep(self, shared):
        return shared["query"]

    def exec(self, query):
        # Retrieve relevant documents
        embeddings = get_embedding(query)
        relevant_docs = vector_search(embeddings, top_k=5)
        return relevant_docs

    def post(self, shared, prep_res, exec_res):
        shared["retrieved_docs"] = exec_res
        return "default"

class GenerationNode(Node):
    def prep(self, shared):
        return {
            "query": shared["query"],
            "context": shared["retrieved_docs"]
        }

    def exec(self, data):
        context_text = "\n".join(data["context"])
        prompt = f"""
        Context: {context_text}

        Question: {data["query"]}

        Answer the question based on the provided context.
        """
        return call_llm(prompt)

    def post(self, shared, prep_res, exec_res):
        shared["answer"] = exec_res
        return "default"
```

## Advanced RAG with Re-ranking

```python
class RetrievalWithRerankingNode(Node):
    def exec(self, query):
        # Initial retrieval
        initial_docs = vector_search(query, top_k=20)

        # Re-rank based on relevance
        reranked_docs = rerank_documents(query, initial_docs, top_k=5)
        return reranked_docs

class ContextualGenerationNode(Node):
    def exec(self, data):
        # Generate with citation tracking
        prompt = f"""
        Context documents:
        {format_docs_with_ids(data["context"])}

        Question: {data["query"]}

        Answer with citations [doc_id].
        """
        return call_llm(prompt)
```

================================================
File: docs/design_pattern/mapreduce.md
================================================

# Map Reduce

Map Reduce splits large tasks into smaller chunks (Map), processes them independently, then combines results (Reduce).

## Basic Map Reduce

```python
class MapNode(BatchNode):
    def prep(self, shared):
        # Split large document into chunks
        document = shared["document"]
        chunk_size = 1000
        chunks = [document[i:i+chunk_size]
                 for i in range(0, len(document), chunk_size)]
        return chunks

    def exec(self, chunk):
        # Process each chunk independently
        summary = call_llm(f"Summarize this text: {chunk}")
        return summary

    def post(self, shared, prep_res, exec_res_list):
        shared["chunk_summaries"] = exec_res_list
        return "default"

class ReduceNode(Node):
    def prep(self, shared):
        return shared["chunk_summaries"]

    def exec(self, summaries):
        # Combine all summaries into final result
        combined_text = "\n".join(summaries)
        final_summary = call_llm(f"""
        Combine these summaries into a coherent final summary:
        {combined_text}
        """)
        return final_summary

    def post(self, shared, prep_res, exec_res):
        shared["final_summary"] = exec_res
        return "default"
```

## Hierarchical Map Reduce

```python
class HierarchicalMapReduce(BatchNode):
    def prep(self, shared):
        # Create hierarchical chunks
        text = shared["text"]
        return create_hierarchical_chunks(text, levels=3)

    def exec(self, chunk_group):
        # Process each level
        if chunk_group["level"] == 0:
            return call_llm(f"Extract key points: {chunk_group['text']}")
        else:
            sub_results = [self.exec(sub_chunk) for sub_chunk in chunk_group["children"]]
            combined = "\n".join(sub_results)
            return call_llm(f"Synthesize these points: {combined}")
```

================================================
File: docs/utility_function/llm.md
================================================

# LLM Wrapper

A utility function to call Large Language Models. This is the most fundamental utility in any LLM application.

## Basic Implementation

```python
# utils/call_llm.py
from openai import OpenAI

def call_llm(prompt, model="gpt-4o", temperature=0.7):
    """
    Call OpenAI's API with a prompt

    Args:
        prompt (str): The input prompt
        model (str): Model to use
        temperature (float): Sampling temperature

    Returns:
        str: The model's response
    """
    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    response = client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}],
        temperature=temperature
    )

    return response.choices[0].message.content

# Test the function
if __name__ == "__main__":
    result = call_llm("What is the capital of France?")
    print(result)
```

## Advanced Features

### Chat History Support

```python
def call_llm_with_history(messages, model="gpt-4o"):
    """
    Call LLM with conversation history

    Args:
        messages (list): List of message dicts with 'role' and 'content'

    Returns:
        str: The model's response
    """
    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    response = client.chat.completions.create(
        model=model,
        messages=messages
    )

    return response.choices[0].message.content

# Usage
messages = [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "What is Python?"},
    {"role": "assistant", "content": "Python is a programming language..."},
    {"role": "user", "content": "What are its main features?"}
]
response = call_llm_with_history(messages)
```

### Caching Support

```python
from functools import lru_cache
import hashlib

@lru_cache(maxsize=1000)
def cached_call_llm(prompt_hash, model="gpt-4o"):
    """Cached version of LLM call"""
    # Note: We hash the prompt to make it cacheable
    prompt = prompt_hash  # In real implementation, you'd store prompt separately
    return call_llm(prompt, model)

def call_llm_with_cache(prompt, model="gpt-4o", use_cache=True):
    """
    Call LLM with optional caching

    Args:
        prompt (str): Input prompt
        model (str): Model to use
        use_cache (bool): Whether to use cache

    Returns:
        str: Model response
    """
    if use_cache:
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()
        return cached_call_llm(prompt_hash, model)
    else:
        return call_llm(prompt, model)
```

### Error Handling and Retries

```python
import time
import random
from typing import Optional

def call_llm_robust(prompt: str,
                   model: str = "gpt-4o",
                   max_retries: int = 3,
                   base_delay: float = 1.0) -> Optional[str]:
    """
    Robust LLM call with exponential backoff retry

    Args:
        prompt: Input prompt
        model: Model to use
        max_retries: Maximum number of retry attempts
        base_delay: Base delay for exponential backoff

    Returns:
        Model response or None if all retries failed
    """
    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    for attempt in range(max_retries + 1):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                timeout=30
            )
            return response.choices[0].message.content

        except Exception as e:
            if attempt == max_retries:
                print(f"Failed after {max_retries} retries: {e}")
                return None

            # Exponential backoff with jitter
            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"Attempt {attempt + 1} failed, retrying in {delay:.2f}s...")
            time.sleep(delay)

    return None
```

### Streaming Support

```python
def call_llm_stream(prompt: str, model: str = "gpt-4o"):
    """
    Stream LLM response for real-time output

    Args:
        prompt: Input prompt
        model: Model to use

    Yields:
        str: Chunks of the response as they arrive
    """
    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    stream = client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}],
        stream=True
    )

    for chunk in stream:
        if chunk.choices[0].delta.content is not None:
            yield chunk.choices[0].delta.content

# Usage
for chunk in call_llm_stream("Write a story about AI"):
    print(chunk, end="", flush=True)
```

## Improvements
Feel free to enhance your `call_llm` function as needed. Here are examples:

- Handle chat history:

```python
def call_llm(messages):
    from openai import OpenAI
    client = OpenAI(api_key="YOUR_API_KEY_HERE")
    r = client.chat.completions.create(
        model="gpt-4o",
        messages=messages
    )
    return r.choices[0].message.content
```

- Add in-memory caching

```python
from functools import lru_cache

@lru_cache(maxsize=1000)
def call_llm(prompt):
    # Your implementation here
    pass
```

> ⚠️ Caching conflicts with Node retries, as retries yield the same result.
>
> To address this, you could use cached results only if not retried.
{: .warning }


```python
from functools import lru_cache

@lru_cache(maxsize=1000)
def cached_call(prompt):
    pass

def call_llm(prompt, use_cache):
    if use_cache:
        return cached_call(prompt)
    # Call the underlying function directly
    return cached_call.__wrapped__(prompt)

class SummarizeNode(Node):
    def exec(self, text):
        return call_llm(f"Summarize: {text}", self.cur_retry==0)
```

- Enable logging:

```python
def call_llm(prompt):
    import logging
    logging.info(f"Prompt: {prompt}")
    response = ... # Your implementation here
    logging.info(f"Response: {response}")
    return response
```

================================================
File: docs/design_pattern/multi_agent.md
================================================

# (Advanced) Multi-Agents

Multi-agent systems coordinate multiple autonomous agents to solve complex problems that require different specialized capabilities.

## Basic Multi-Agent Pattern

```python
class CoordinatorAgent(Node):
    def prep(self, shared):
        return {
            "task": shared["task"],
            "available_agents": ["researcher", "writer", "reviewer"],
            "current_state": shared.get("state", "planning")
        }

    def exec(self, context):
        prompt = f"""
        Task: {context["task"]}
        Available agents: {context["available_agents"]}
        Current state: {context["current_state"]}

        Which agent should handle the next step? Respond with agent name.
        """
        decision = call_llm(prompt)
        return decision.strip().lower()

    def post(self, shared, prep_res, exec_res):
        shared["next_agent"] = exec_res
        return exec_res

class ResearcherAgent(Node):
    def exec(self, task):
        research_prompt = f"Research information about: {task}"
        return call_llm(research_prompt)

    def post(self, shared, prep_res, exec_res):
        shared["research_results"] = exec_res
        return "coordinate"

class WriterAgent(Node):
    def prep(self, shared):
        return {
            "task": shared["task"],
            "research": shared.get("research_results", "")
        }

    def exec(self, context):
        write_prompt = f"""
        Task: {context["task"]}
        Research: {context["research"]}

        Write content based on the research.
        """
        return call_llm(write_prompt)

    def post(self, shared, prep_res, exec_res):
        shared["draft"] = exec_res
        return "coordinate"

# Connect agents through coordinator
coordinator = CoordinatorAgent()
researcher = ResearcherAgent()
writer = WriterAgent()

coordinator - "researcher" >> researcher
coordinator - "writer" >> writer
researcher - "coordinate" >> coordinator
writer - "coordinate" >> coordinator

multi_agent_flow = Flow(start=coordinator)
```

## Agent Communication Patterns

### Message Passing
```python
class MessageBus(Node):
    def prep(self, shared):
        return shared.get("messages", [])

    def exec(self, messages):
        # Process and route messages between agents
        return route_messages(messages)

    def post(self, shared, prep_res, exec_res):
        shared["messages"] = exec_res
        return "default"
```

### Shared Memory
```python
shared = {
    "agents": {
        "researcher": {"status": "idle", "results": []},
        "writer": {"status": "idle", "draft": ""},
        "reviewer": {"status": "idle", "feedback": ""}
    },
    "global_state": "planning",
    "task_queue": []
}
```

================================================
File: docs/design_pattern/workflow.md
================================================

# Workflow

Many real-world tasks are too complex for one LLM call. The solution is to **Task Decomposition**: decompose them into a chain of multiple Nodes.

## Basic Workflow Pattern

```python
class GenerateOutline(Node):
    def prep(self, shared):
        return shared["topic"]

    def exec(self, topic):
        return call_llm(f"Create a detailed outline for an article about {topic}")

    def post(self, shared, prep_res, exec_res):
        shared["outline"] = exec_res
        return "default"

class WriteSection(Node):
    def prep(self, shared):
        return shared["outline"]

    def exec(self, outline):
        return call_llm(f"Write content based on this outline: {outline}")

    def post(self, shared, prep_res, exec_res):
        shared["draft"] = exec_res
        return "default"

class ReviewAndRefine(Node):
    def prep(self, shared):
        return shared["draft"]

    def exec(self, draft):
        return call_llm(f"Review and improve this draft: {draft}")

    def post(self, shared, prep_res, exec_res):
        shared["final_article"] = exec_res
        return "default"

# Connect nodes
outline = GenerateOutline()
write = WriteSection()
review = ReviewAndRefine()

outline >> write >> review

# Create and run flow
writing_flow = Flow(start=outline)
shared = {"topic": "AI Safety"}
writing_flow.run(shared)
```

## Conditional Workflows

```python
class QualityCheck(Node):
    def exec(self, content):
        quality_score = assess_quality(content)
        if quality_score > 0.8:
            return "approved"
        else:
            return "needs_revision"

    def post(self, shared, prep_res, exec_res):
        shared["quality_status"] = exec_res
        return exec_res

# Branching workflow
write_node = WriteSection()
quality_node = QualityCheck()
revision_node = RevisionNode()
publish_node = PublishNode()

write_node >> quality_node
quality_node - "approved" >> publish_node
quality_node - "needs_revision" >> revision_node
revision_node >> quality_node  # Loop back for re-check
```

================================================
File: docs/utility_function/websearch.md
================================================

# Web Search

Utility function for searching the web and retrieving relevant information.

## Basic Implementation

```python
# utils/search_web.py
import requests
from typing import List, Dict

def search_web(query: str, num_results: int = 5) -> List[Dict]:
    """
    Search the web using a search API

    Args:
        query: Search query string
        num_results: Number of results to return

    Returns:
        List of search results with title, url, and snippet
    """
    # Example using Google Custom Search API
    api_key = "YOUR_API_KEY"
    search_engine_id = "YOUR_SEARCH_ENGINE_ID"

    url = "https://www.googleapis.com/customsearch/v1"
    params = {
        "key": api_key,
        "cx": search_engine_id,
        "q": query,
        "num": num_results
    }

    response = requests.get(url, params=params)
    data = response.json()

    results = []
    for item in data.get("items", []):
        results.append({
            "title": item.get("title", ""),
            "url": item.get("link", ""),
            "snippet": item.get("snippet", "")
        })

    return results

# Test the function
if __name__ == "__main__":
    results = search_web("Python programming")
    for result in results:
        print(f"Title: {result['title']}")
        print(f"URL: {result['url']}")
        print(f"Snippet: {result['snippet']}")
        print("-" * 50)
```

## Alternative Implementations

### Using DuckDuckGo
```python
def search_duckduckgo(query: str, num_results: int = 5) -> List[Dict]:
    """Search using DuckDuckGo API"""
    from duckduckgo_search import DDGS

    results = []
    with DDGS() as ddgs:
        for result in ddgs.text(query, max_results=num_results):
            results.append({
                "title": result.get("title", ""),
                "url": result.get("href", ""),
                "snippet": result.get("body", "")
            })

    return results
```

### Using Bing Search API
```python
def search_bing(query: str, num_results: int = 5) -> List[Dict]:
    """Search using Bing Search API"""
    subscription_key = "YOUR_BING_API_KEY"
    endpoint = "https://api.bing.microsoft.com/v7.0/search"

    headers = {"Ocp-Apim-Subscription-Key": subscription_key}
    params = {"q": query, "count": num_results}

    response = requests.get(endpoint, headers=headers, params=params)
    data = response.json()

    results = []
    for item in data.get("webPages", {}).get("value", []):
        results.append({
            "title": item.get("name", ""),
            "url": item.get("url", ""),
            "snippet": item.get("snippet", "")
        })

    return results
```

================================================
File: docs/utility_function/embedding.md
================================================

# Embedding

Utility function for generating text embeddings using various embedding models.

## Basic Implementation

```python
# utils/get_embedding.py
import openai
from typing import List

def get_embedding(text: str, model: str = "text-embedding-3-small") -> List[float]:
    """
    Get embedding for a text string using OpenAI's embedding API

    Args:
        text: Input text to embed
        model: Embedding model to use

    Returns:
        List of floats representing the embedding vector
    """
    client = openai.OpenAI(api_key="YOUR_API_KEY_HERE")

    response = client.embeddings.create(
        model=model,
        input=text
    )

    return response.data[0].embedding

# Test the function
if __name__ == "__main__":
    text = "This is a sample text for embedding"
    embedding = get_embedding(text)
    print(f"Embedding dimension: {len(embedding)}")
    print(f"First 5 values: {embedding[:5]}")
```

## Alternative Implementations

### Using Sentence Transformers
```python
def get_embedding_sentence_transformers(text: str, model_name: str = "all-MiniLM-L6-v2") -> List[float]:
    """Get embedding using sentence-transformers library"""
    from sentence_transformers import SentenceTransformer

    model = SentenceTransformer(model_name)
    embedding = model.encode(text)
    return embedding.tolist()
```

### Using Hugging Face Transformers
```python
def get_embedding_huggingface(text: str, model_name: str = "sentence-transformers/all-MiniLM-L6-v2") -> List[float]:
    """Get embedding using Hugging Face transformers"""
    from transformers import AutoTokenizer, AutoModel
    import torch

    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModel.from_pretrained(model_name)

    inputs = tokenizer(text, return_tensors="pt", padding=True, truncation=True)

    with torch.no_grad():
        outputs = model(**inputs)
        # Use mean pooling
        embeddings = outputs.last_hidden_state.mean(dim=1)

    return embeddings.squeeze().tolist()
```

### Batch Embedding
```python
def get_embeddings_batch(texts: List[str], model: str = "text-embedding-3-small") -> List[List[float]]:
    """Get embeddings for multiple texts in batch"""
    client = openai.OpenAI(api_key="YOUR_API_KEY_HERE")

    response = client.embeddings.create(
        model=model,
        input=texts
    )

    return [data.embedding for data in response.data]
```

================================================
File: docs/utility_function/chunking.md
================================================

# Chunking

Utility function for splitting large texts into smaller, manageable chunks.

## Basic Implementation

```python
# utils/chunking.py
from typing import List

def chunk_text(text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
    """
    Split text into overlapping chunks

    Args:
        text: Input text to chunk
        chunk_size: Maximum size of each chunk
        overlap: Number of characters to overlap between chunks

    Returns:
        List of text chunks
    """
    if len(text) <= chunk_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size

        # If this isn't the last chunk, try to break at a sentence boundary
        if end < len(text):
            # Look for sentence endings within the last 100 characters
            last_period = text.rfind('.', start, end)
            last_exclamation = text.rfind('!', start, end)
            last_question = text.rfind('?', start, end)

            sentence_end = max(last_period, last_exclamation, last_question)

            if sentence_end > start + chunk_size // 2:  # Only break if we're not too far back
                end = sentence_end + 1

        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        # Move start position with overlap
        start = end - overlap

        # Ensure we don't go backwards
        if start <= chunks[-1] if chunks else 0:
            start = end

    return chunks

# Test the function
if __name__ == "__main__":
    sample_text = "This is a long text. " * 100
    chunks = chunk_text(sample_text, chunk_size=200, overlap=50)
    print(f"Original length: {len(sample_text)}")
    print(f"Number of chunks: {len(chunks)}")
    for i, chunk in enumerate(chunks[:3]):
        print(f"Chunk {i+1}: {chunk[:50]}...")
```

## Advanced Chunking Strategies

### Semantic Chunking
```python
def chunk_by_sentences(text: str, max_sentences: int = 5) -> List[str]:
    """Chunk text by sentence boundaries"""
    import re

    # Split by sentence endings
    sentences = re.split(r'[.!?]+', text)
    sentences = [s.strip() for s in sentences if s.strip()]

    chunks = []
    current_chunk = []

    for sentence in sentences:
        current_chunk.append(sentence)

        if len(current_chunk) >= max_sentences:
            chunks.append('. '.join(current_chunk) + '.')
            current_chunk = []

    # Add remaining sentences
    if current_chunk:
        chunks.append('. '.join(current_chunk) + '.')

    return chunks
```

### Paragraph-based Chunking
```python
def chunk_by_paragraphs(text: str, max_paragraphs: int = 3) -> List[str]:
    """Chunk text by paragraph boundaries"""
    paragraphs = text.split('\n\n')
    paragraphs = [p.strip() for p in paragraphs if p.strip()]

    chunks = []
    current_chunk = []

    for paragraph in paragraphs:
        current_chunk.append(paragraph)

        if len(current_chunk) >= max_paragraphs:
            chunks.append('\n\n'.join(current_chunk))
            current_chunk = []

    if current_chunk:
        chunks.append('\n\n'.join(current_chunk))

    return chunks
```

### Token-based Chunking
```python
def chunk_by_tokens(text: str, max_tokens: int = 500, model: str = "gpt-4") -> List[str]:
    """Chunk text by token count"""
    import tiktoken

    encoding = tiktoken.encoding_for_model(model)
    tokens = encoding.encode(text)

    chunks = []
    start = 0

    while start < len(tokens):
        end = start + max_tokens
        chunk_tokens = tokens[start:end]
        chunk_text = encoding.decode(chunk_tokens)
        chunks.append(chunk_text)
        start = end

    return chunks
```

================================================
File: docs/utility_function/vector.md
================================================

# Vector Databases

Utility functions for working with vector databases to store and search embeddings.

## Basic Implementation with FAISS

```python
# utils/vector_db.py
import faiss
import numpy as np
from typing import List, Tuple

def create_index(embeddings: List[List[float]]) -> faiss.Index:
    """
    Create a FAISS index from embeddings

    Args:
        embeddings: List of embedding vectors

    Returns:
        FAISS index for similarity search
    """
    embeddings_array = np.array(embeddings).astype('float32')
    dimension = embeddings_array.shape[1]

    # Create index
    index = faiss.IndexFlatL2(dimension)
    index.add(embeddings_array)

    return index

def search_index(index: faiss.Index, query_embedding: List[float], top_k: int = 5) -> Tuple[List[List[int]], List[List[float]]]:
    """
    Search the index for similar vectors

    Args:
        index: FAISS index
        query_embedding: Query vector
        top_k: Number of results to return

    Returns:
        Tuple of (indices, distances) for top_k results
    """
    query_array = np.array([query_embedding]).astype('float32')
    distances, indices = index.search(query_array, top_k)

    return indices.tolist(), distances.tolist()

# Test the functions
if __name__ == "__main__":
    # Sample embeddings (normally from get_embedding function)
    sample_embeddings = [
        [0.1, 0.2, 0.3, 0.4],
        [0.2, 0.3, 0.4, 0.5],
        [0.3, 0.4, 0.5, 0.6]
    ]

    index = create_index(sample_embeddings)
    query = [0.15, 0.25, 0.35, 0.45]
    indices, distances = search_index(index, query, top_k=2)

    print(f"Top results: {indices}")
    print(f"Distances: {distances}")
```

## Advanced Vector Database Implementations

### Using Chroma
```python
def create_chroma_collection(embeddings: List[List[float]], documents: List[str], collection_name: str = "default"):
    """Create a Chroma collection"""
    import chromadb

    client = chromadb.Client()
    collection = client.create_collection(name=collection_name)

    # Add documents with embeddings
    ids = [f"doc_{i}" for i in range(len(documents))]
    collection.add(
        embeddings=embeddings,
        documents=documents,
        ids=ids
    )

    return collection

def search_chroma(collection, query_embedding: List[float], top_k: int = 5):
    """Search Chroma collection"""
    results = collection.query(
        query_embeddings=[query_embedding],
        n_results=top_k
    )
    return results
```

### Using Pinecone
```python
def create_pinecone_index(index_name: str, dimension: int):
    """Create a Pinecone index"""
    import pinecone

    pinecone.init(api_key="YOUR_API_KEY", environment="YOUR_ENV")

    if index_name not in pinecone.list_indexes():
        pinecone.create_index(index_name, dimension=dimension)

    return pinecone.Index(index_name)

def upsert_to_pinecone(index, embeddings: List[List[float]], ids: List[str]):
    """Upload embeddings to Pinecone"""
    vectors = [(ids[i], embeddings[i]) for i in range(len(embeddings))]
    index.upsert(vectors)

def search_pinecone(index, query_embedding: List[float], top_k: int = 5):
    """Search Pinecone index"""
    results = index.query(
        vector=query_embedding,
        top_k=top_k,
        include_metadata=True
    )
    return results
```

### Using Weaviate
```python
def create_weaviate_client():
    """Create Weaviate client"""
    import weaviate

    client = weaviate.Client("http://localhost:8080")
    return client

def add_to_weaviate(client, documents: List[str], embeddings: List[List[float]], class_name: str = "Document"):
    """Add documents to Weaviate"""
    for i, (doc, embedding) in enumerate(zip(documents, embeddings)):
        data_object = {
            "content": doc,
            "doc_id": f"doc_{i}"
        }

        client.data_object.create(
            data_object=data_object,
            class_name=class_name,
            vector=embedding
        )

def search_weaviate(client, query_embedding: List[float], class_name: str = "Document", top_k: int = 5):
    """Search Weaviate"""
    result = client.query.get(class_name, ["content", "doc_id"]) \
        .with_near_vector({"vector": query_embedding}) \
        .with_limit(top_k) \
        .do()

    return result
```

================================================
File: docs/utility_function/viz.md
================================================

# Visualization and Debug

Utility functions for visualizing flows and debugging node execution.

## Basic Flow Visualization

```python
# utils/viz_debug.py
import json
from typing import Dict, Any

def visualize_shared_store(shared: Dict[str, Any], title: str = "Shared Store State"):
    """
    Pretty print the shared store state

    Args:
        shared: The shared store dictionary
        title: Title for the visualization
    """
    print(f"\n{'='*50}")
    print(f"{title:^50}")
    print(f"{'='*50}")

    def print_nested(obj, indent=0):
        spaces = "  " * indent
        if isinstance(obj, dict):
            for key, value in obj.items():
                if isinstance(value, (dict, list)):
                    print(f"{spaces}{key}:")
                    print_nested(value, indent + 1)
                else:
                    # Truncate long strings
                    if isinstance(value, str) and len(value) > 100:
                        value = value[:97] + "..."
                    print(f"{spaces}{key}: {value}")
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                print(f"{spaces}[{i}]:")
                print_nested(item, indent + 1)
        else:
            print(f"{spaces}{obj}")

    print_nested(shared)
    print(f"{'='*50}\n")

def log_node_execution(node_name: str, step: str, data: Any = None):
    """
    Log node execution steps for debugging

    Args:
        node_name: Name of the node
        step: Execution step (prep, exec, post)
        data: Optional data to log
    """
    timestamp = __import__('datetime').datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {node_name}.{step}()")

    if data is not None:
        if isinstance(data, str) and len(data) > 200:
            print(f"  Data: {data[:197]}...")
        else:
            print(f"  Data: {data}")

# Test the functions
if __name__ == "__main__":
    sample_shared = {
        "user": {"id": "123", "name": "Alice"},
        "documents": ["doc1.txt", "doc2.txt"],
        "results": {
            "summary": "This is a long summary that might be truncated...",
            "score": 0.85
        }
    }

    visualize_shared_store(sample_shared, "Sample State")
    log_node_execution("SummarizeNode", "exec", "Processing document...")
```

## Flow Execution Tracing

```python
class FlowTracer:
    """Trace flow execution for debugging"""

    def __init__(self):
        self.execution_log = []

    def trace_node(self, node_name: str, step: str, input_data: Any = None, output_data: Any = None):
        """Record node execution"""
        entry = {
            "timestamp": __import__('datetime').datetime.now().isoformat(),
            "node": node_name,
            "step": step,
            "input": self._truncate_data(input_data),
            "output": self._truncate_data(output_data)
        }
        self.execution_log.append(entry)

    def _truncate_data(self, data: Any, max_length: int = 100) -> Any:
        """Truncate data for logging"""
        if isinstance(data, str) and len(data) > max_length:
            return data[:max_length-3] + "..."
        elif isinstance(data, (list, dict)) and len(str(data)) > max_length:
            return f"{type(data).__name__} with {len(data)} items"
        return data

    def print_trace(self):
        """Print execution trace"""
        print("\n" + "="*80)
        print("FLOW EXECUTION TRACE")
        print("="*80)

        for entry in self.execution_log:
            print(f"[{entry['timestamp']}] {entry['node']}.{entry['step']}()")
            if entry['input'] is not None:
                print(f"  Input: {entry['input']}")
            if entry['output'] is not None:
                print(f"  Output: {entry['output']}")
            print()

    def save_trace(self, filename: str):
        """Save trace to JSON file"""
        with open(filename, 'w') as f:
            json.dump(self.execution_log, f, indent=2)

# Usage example
tracer = FlowTracer()
tracer.trace_node("LoadData", "exec", input_data="file.txt", output_data="File content loaded")
tracer.trace_node("ProcessData", "exec", input_data="File content", output_data="Processed result")
tracer.print_trace()
```

## Performance Monitoring

```python
import time
from functools import wraps

def monitor_performance(func):
    """Decorator to monitor function performance"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        execution_time = end_time - start_time
        print(f"⏱️  {func.__name__} executed in {execution_time:.3f} seconds")

        return result
    return wrapper

class PerformanceTracker:
    """Track performance metrics across flow execution"""

    def __init__(self):
        self.metrics = {}

    def start_timer(self, operation: str):
        """Start timing an operation"""
        self.metrics[operation] = {"start": time.time()}

    def end_timer(self, operation: str):
        """End timing an operation"""
        if operation in self.metrics:
            self.metrics[operation]["end"] = time.time()
            self.metrics[operation]["duration"] = (
                self.metrics[operation]["end"] - self.metrics[operation]["start"]
            )

    def print_summary(self):
        """Print performance summary"""
        print("\n" + "="*50)
        print("PERFORMANCE SUMMARY")
        print("="*50)

        total_time = 0
        for operation, data in self.metrics.items():
            if "duration" in data:
                duration = data["duration"]
                total_time += duration
                print(f"{operation}: {duration:.3f}s")

        print(f"\nTotal execution time: {total_time:.3f}s")
        print("="*50)

# Usage example
tracker = PerformanceTracker()
tracker.start_timer("data_loading")
time.sleep(0.1)  # Simulate work
tracker.end_timer("data_loading")
tracker.print_summary()
```

================================================
File: docs/utility_function/text_to_speech.md
================================================

# Text-to-Speech

Utility function for converting text to speech using various TTS services.

## Basic Implementation

```python
# utils/text_to_speech.py
from typing import Optional
import os

def text_to_speech(text: str, output_file: str = "output.mp3", voice: str = "alloy") -> str:
    """
    Convert text to speech using OpenAI's TTS API

    Args:
        text: Text to convert to speech
        output_file: Output audio file path
        voice: Voice to use (alloy, echo, fable, onyx, nova, shimmer)

    Returns:
        Path to the generated audio file
    """
    from openai import OpenAI

    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    response = client.audio.speech.create(
        model="tts-1",
        voice=voice,
        input=text
    )

    response.stream_to_file(output_file)
    return output_file

# Test the function
if __name__ == "__main__":
    sample_text = "Hello, this is a test of the text-to-speech functionality."
    audio_file = text_to_speech(sample_text, "test_output.mp3")
    print(f"Audio saved to: {audio_file}")
```

## Alternative Implementations

### Using Google Text-to-Speech
```python
def text_to_speech_google(text: str, output_file: str = "output.mp3", language: str = "en-US") -> str:
    """Convert text to speech using Google Cloud TTS"""
    from google.cloud import texttospeech

    client = texttospeech.TextToSpeechClient()

    synthesis_input = texttospeech.SynthesisInput(text=text)
    voice = texttospeech.VoiceSelectionParams(
        language_code=language,
        ssml_gender=texttospeech.SsmlVoiceGender.NEUTRAL
    )
    audio_config = texttospeech.AudioConfig(
        audio_encoding=texttospeech.AudioEncoding.MP3
    )

    response = client.synthesize_speech(
        input=synthesis_input,
        voice=voice,
        audio_config=audio_config
    )

    with open(output_file, "wb") as out:
        out.write(response.audio_content)

    return output_file
```

### Using Azure Cognitive Services
```python
def text_to_speech_azure(text: str, output_file: str = "output.wav", voice: str = "en-US-AriaNeural") -> str:
    """Convert text to speech using Azure Cognitive Services"""
    import azure.cognitiveservices.speech as speechsdk

    speech_key = "YOUR_SPEECH_KEY"
    service_region = "YOUR_SERVICE_REGION"

    speech_config = speechsdk.SpeechConfig(subscription=speech_key, region=service_region)
    speech_config.speech_synthesis_voice_name = voice

    audio_config = speechsdk.audio.AudioOutputConfig(filename=output_file)
    synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config, audio_config=audio_config)

    result = synthesizer.speak_text_async(text).get()

    if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
        return output_file
    else:
        raise Exception(f"Speech synthesis failed: {result.reason}")
```

### Using pyttsx3 (Offline)
```python
def text_to_speech_offline(text: str, output_file: Optional[str] = None, rate: int = 200) -> Optional[str]:
    """Convert text to speech using offline pyttsx3"""
    import pyttsx3

    engine = pyttsx3.init()
    engine.setProperty('rate', rate)

    if output_file:
        engine.save_to_file(text, output_file)
        engine.runAndWait()
        return output_file
    else:
        engine.say(text)
        engine.runAndWait()
        return None
```

## Advanced Features

### Batch Text-to-Speech
```python
def batch_text_to_speech(texts: list[str], output_dir: str = "audio_output", voice: str = "alloy") -> list[str]:
    """Convert multiple texts to speech"""
    os.makedirs(output_dir, exist_ok=True)
    output_files = []

    for i, text in enumerate(texts):
        output_file = os.path.join(output_dir, f"audio_{i+1}.mp3")
        result_file = text_to_speech(text, output_file, voice)
        output_files.append(result_file)

    return output_files
```

### SSML Support
```python
def text_to_speech_ssml(ssml_text: str, output_file: str = "output.mp3") -> str:
    """Convert SSML to speech for advanced control"""
    from openai import OpenAI

    client = OpenAI(api_key="YOUR_API_KEY_HERE")

    response = client.audio.speech.create(
        model="tts-1",
        voice="alloy",
        input=ssml_text,
        response_format="mp3"
    )

    response.stream_to_file(output_file)
    return output_file

# Example SSML usage
ssml_example = """
<speak>
    <prosody rate="slow">This text is spoken slowly.</prosody>
    <break time="1s"/>
    <prosody rate="fast">This text is spoken quickly.</prosody>
    <break time="2s"/>
    <emphasis level="strong">This text is emphasized.</emphasis>
</speak>
"""
```

## Integration with PocketFlow

### TTS Node Example
```python
class TextToSpeechNode(Node):
    def prep(self, shared):
        return shared["text_to_speak"]

    def exec(self, text):
        output_file = f"speech_{int(time.time())}.mp3"
        return text_to_speech(text, output_file)

    def post(self, shared, prep_res, exec_res):
        shared["audio_file"] = exec_res
        return "default"

# Usage in flow
tts_node = TextToSpeechNode()
flow = Flow(start=tts_node)
shared = {"text_to_speak": "Hello, world!"}
flow.run(shared)
print(f"Audio generated: {shared['audio_file']}")
```
```
