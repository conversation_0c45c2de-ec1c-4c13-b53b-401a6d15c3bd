import google.generativeai as genai
import os
import logging

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    logging.info("Loaded environment variables from .env file")
except ImportError:
    logging.warning("python-dotenv not installed. Install with: pip install python-dotenv")
except Exception as e:
    logging.warning(f"Could not load .env file: {e}")

# Configure Gemini API
api_key = os.environ.get("GEMINI_API_KEY")
if api_key and api_key != "your-api-key":
    genai.configure(api_key=api_key)
    logging.info("Gemini API configured successfully")
else:
    logging.warning("GEMINI_API_KEY not found or invalid")

def call_llm(prompt, model="gemini-1.5-flash", temperature=0.1, max_tokens=4096):
    """
    Call Google Gemini LLM with the given prompt.

    Args:
        prompt: The prompt to send to the LLM
        model: Gemini model to use (default: gemini-1.5-flash)
        temperature: Temperature for response generation (0.0-1.0)
        max_tokens: Maximum tokens in response

    Returns:
        String response from the LLM
    """
    try:
        # Create the model instance
        model_instance = genai.GenerativeModel(model)

        # Configure generation parameters
        generation_config = genai.types.GenerationConfig(
            temperature=temperature,
            max_output_tokens=max_tokens,
        )

        # Generate response
        response = model_instance.generate_content(
            prompt,
            generation_config=generation_config
        )

        # Extract text from response
        if response.text:
            return response.text
        else:
            logging.warning("Empty response from Gemini")
            return ""

    except Exception as e:
        logging.error(f"Gemini API call failed: {e}")
        # Fallback response for critical operations
        return "Error: Unable to get LLM response. Please check your GEMINI_API_KEY and try again."

def call_llm_with_json_response(prompt, model="gemini-1.5-flash"):
    """
    Call Gemini LLM expecting a JSON response.

    Args:
        prompt: The prompt to send to the LLM (should request JSON format)
        model: Gemini model to use

    Returns:
        Parsed JSON object or empty dict if parsing fails
    """
    import json

    # Add JSON formatting instruction to prompt
    json_prompt = f"""{prompt}

Please respond with valid JSON only. Do not include any explanatory text before or after the JSON."""

    try:
        response = call_llm(json_prompt, model, temperature=0.0)

        # Try to extract JSON from response
        response = response.strip()

        # Remove markdown code blocks if present
        if response.startswith("```json"):
            response = response[7:]
        if response.startswith("```"):
            response = response[3:]
        if response.endswith("```"):
            response = response[:-3]

        response = response.strip()

        # Parse JSON
        return json.loads(response)

    except json.JSONDecodeError as e:
        logging.warning(f"Failed to parse JSON response: {e}")
        logging.warning(f"Raw response: {response[:200]}...")
        return {}
    except Exception as e:
        logging.error(f"JSON LLM call failed: {e}")
        return {}

if __name__ == "__main__":
    # Test the function
    prompt = "What is the meaning of life?"
    print("Testing Gemini LLM call...")
    response = call_llm(prompt)
    print(f"Response: {response}")

    # Test JSON response
    json_prompt = """
    Analyze this simple Python code and respond with JSON:

    ```python
    print("Hello World")
    ```

    Respond with JSON containing:
    {
        "language": "python",
        "complexity": "simple",
        "functions": []
    }
    """

    print("\nTesting JSON response...")
    json_response = call_llm_with_json_response(json_prompt)
    print(f"JSON Response: {json_response}")
