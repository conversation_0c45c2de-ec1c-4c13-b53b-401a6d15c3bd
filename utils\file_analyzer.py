"""
File content analyzer for extracting containerization-relevant information.
"""
import os
import re
import json
import yaml
from typing import Dict, List, Optional, Any, Set
import mimetypes


def analyze_file(file_path: str, file_content: str) -> Dict[str, Any]:
    """
    Analyze a file and extract relevant information for containerization.
    
    Args:
        file_path: Path to the file
        file_content: Content of the file
    
    Returns:
        Structured analysis of the file
    """
    analysis = {
        'file_path': file_path,
        'file_type': get_file_type(file_path),
        'size': len(file_content),
        'language': detect_language(file_path),
        'framework': None,
        'dependencies': [],
        'ports': [],
        'environment_variables': [],
        'database_connections': [],
        'external_services': [],
        'entry_points': [],
        'configuration': {},
        'security_issues': [],
        'containerization_hints': []
    }
    
    # Analyze based on file type
    if analysis['file_type'] == 'package_manager':
        analysis.update(analyze_package_file(file_path, file_content))
    elif analysis['file_type'] == 'configuration':
        analysis.update(analyze_config_file(file_path, file_content))
    elif analysis['file_type'] == 'source_code':
        analysis.update(analyze_source_code(file_path, file_content, analysis['language']))
    elif analysis['file_type'] == 'docker':
        analysis.update(analyze_docker_file(file_path, file_content))
    elif analysis['file_type'] == 'documentation':
        analysis.update(analyze_documentation(file_path, file_content))
    
    return analysis


def get_file_type(file_path: str) -> str:
    """Determine the type of file for analysis purposes."""
    filename = os.path.basename(file_path).lower()
    extension = os.path.splitext(filename)[1].lower()
    
    # Package manager files
    package_files = {
        'package.json', 'requirements.txt', 'pipfile', 'poetry.lock', 'pyproject.toml',
        'go.mod', 'go.sum', 'cargo.toml', 'cargo.lock', 'composer.json', 'gemfile'
    }
    if filename in package_files:
        return 'package_manager'
    
    # Configuration files
    config_patterns = [
        r'.*\.ya?ml$', r'.*\.json$', r'.*\.toml$', r'.*\.ini$', r'.*\.conf$',
        r'.*config.*', r'.*settings.*', r'\.env.*', r'.*\.properties$'
    ]
    if any(re.match(pattern, filename) for pattern in config_patterns):
        return 'configuration'
    
    # Docker files
    if filename in ['dockerfile', 'docker-compose.yml', 'docker-compose.yaml', '.dockerignore']:
        return 'docker'
    
    # Documentation
    doc_extensions = {'.md', '.rst', '.txt', '.adoc'}
    if extension in doc_extensions or filename in ['readme', 'changelog', 'license']:
        return 'documentation'
    
    # Source code
    code_extensions = {
        '.py', '.js', '.ts', '.go', '.rs', '.java', '.cpp', '.c', '.cs', '.php',
        '.rb', '.scala', '.kt', '.swift', '.dart', '.r', '.jl', '.hs', '.elm'
    }
    if extension in code_extensions:
        return 'source_code'
    
    return 'other'


def detect_language(file_path: str) -> Optional[str]:
    """Detect programming language from file extension."""
    extension = os.path.splitext(file_path)[1].lower()
    
    language_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.go': 'go',
        '.rs': 'rust',
        '.java': 'java',
        '.cpp': 'cpp',
        '.c': 'c',
        '.cs': 'csharp',
        '.php': 'php',
        '.rb': 'ruby',
        '.scala': 'scala',
        '.kt': 'kotlin',
        '.swift': 'swift',
        '.dart': 'dart',
        '.r': 'r',
        '.jl': 'julia',
        '.hs': 'haskell',
        '.elm': 'elm'
    }
    
    return language_map.get(extension)


def analyze_package_file(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze package manager files for dependencies."""
    filename = os.path.basename(file_path).lower()
    analysis = {}
    
    try:
        if filename == 'package.json':
            data = json.loads(content)
            analysis['dependencies'] = list(data.get('dependencies', {}).keys())
            analysis['dev_dependencies'] = list(data.get('devDependencies', {}).keys())
            analysis['scripts'] = data.get('scripts', {})
            analysis['entry_points'] = [data.get('main', 'index.js')]
            
            # Check for common frameworks
            deps = analysis['dependencies'] + analysis['dev_dependencies']
            if 'express' in deps:
                analysis['framework'] = 'express'
            elif 'react' in deps:
                analysis['framework'] = 'react'
            elif 'vue' in deps:
                analysis['framework'] = 'vue'
            elif 'angular' in deps:
                analysis['framework'] = 'angular'
        
        elif filename == 'requirements.txt':
            lines = content.strip().split('\n')
            analysis['dependencies'] = [line.split('==')[0].split('>=')[0].split('<=')[0].strip() 
                                      for line in lines if line.strip() and not line.startswith('#')]
            
            # Check for common frameworks
            deps = analysis['dependencies']
            if 'django' in deps:
                analysis['framework'] = 'django'
            elif 'flask' in deps:
                analysis['framework'] = 'flask'
            elif 'fastapi' in deps:
                analysis['framework'] = 'fastapi'
        
        elif filename in ['go.mod', 'go.sum']:
            # Basic Go module analysis
            lines = content.strip().split('\n')
            analysis['dependencies'] = []
            for line in lines:
                if line.strip().startswith('require'):
                    # Extract module name
                    parts = line.split()
                    if len(parts) >= 2:
                        analysis['dependencies'].append(parts[1])
        
        elif filename in ['cargo.toml', 'cargo.lock']:
            # Basic Rust cargo analysis
            if filename == 'cargo.toml':
                try:
                    data = yaml.safe_load(content)  # TOML is similar to YAML for basic parsing
                    if 'dependencies' in data:
                        analysis['dependencies'] = list(data['dependencies'].keys())
                except:
                    pass
    
    except (json.JSONDecodeError, yaml.YAMLError, Exception):
        pass
    
    return analysis


def analyze_config_file(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze configuration files."""
    analysis = {'configuration': {}}
    
    # Look for environment variables
    env_pattern = r'\$\{?([A-Z_][A-Z0-9_]*)\}?'
    env_vars = re.findall(env_pattern, content)
    analysis['environment_variables'] = list(set(env_vars))
    
    # Look for port configurations
    port_pattern = r'(?:port|PORT).*?(\d{4,5})'
    ports = re.findall(port_pattern, content)
    analysis['ports'] = [int(p) for p in ports if 1000 <= int(p) <= 65535]
    
    # Look for database connections
    db_patterns = [
        r'(?:mongodb|mongo)://[^\s\'"]+',
        r'(?:postgresql|postgres)://[^\s\'"]+',
        r'(?:mysql)://[^\s\'"]+',
        r'(?:redis)://[^\s\'"]+',
        r'(?:sqlite)://[^\s\'"]+',
    ]
    
    for pattern in db_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            analysis['database_connections'].extend(matches)
    
    return analysis


def analyze_source_code(file_path: str, content: str, language: str) -> Dict[str, Any]:
    """Analyze source code files for containerization information."""
    analysis = {}
    
    if language == 'python':
        analysis.update(analyze_python_code(content))
    elif language in ['javascript', 'typescript']:
        analysis.update(analyze_javascript_code(content))
    elif language == 'go':
        analysis.update(analyze_go_code(content))
    elif language == 'java':
        analysis.update(analyze_java_code(content))
    
    return analysis


def analyze_python_code(content: str) -> Dict[str, Any]:
    """Analyze Python source code."""
    analysis = {
        'imports': [],
        'ports': [],
        'environment_variables': [],
        'database_connections': []
    }
    
    # Extract imports
    import_pattern = r'(?:from\s+(\S+)\s+import|import\s+(\S+))'
    imports = re.findall(import_pattern, content)
    analysis['imports'] = [imp[0] or imp[1] for imp in imports]
    
    # Look for Flask/Django/FastAPI apps
    if 'flask' in content.lower():
        analysis['framework'] = 'flask'
        # Look for app.run() calls
        run_pattern = r'app\.run\([^)]*port\s*=\s*(\d+)'
        ports = re.findall(run_pattern, content)
        analysis['ports'].extend([int(p) for p in ports])
    
    if 'django' in content.lower():
        analysis['framework'] = 'django'
    
    if 'fastapi' in content.lower():
        analysis['framework'] = 'fastapi'
    
    # Look for environment variables
    env_pattern = r'os\.environ\.get\([\'"]([A-Z_][A-Z0-9_]*)[\'"]'
    env_vars = re.findall(env_pattern, content)
    analysis['environment_variables'] = env_vars
    
    return analysis


def analyze_javascript_code(content: str) -> Dict[str, Any]:
    """Analyze JavaScript/TypeScript source code."""
    analysis = {
        'imports': [],
        'ports': [],
        'environment_variables': [],
        'database_connections': []
    }
    
    # Look for Express.js
    if 'express' in content.lower():
        analysis['framework'] = 'express'
        # Look for listen() calls
        listen_pattern = r'\.listen\s*\(\s*(\d+)'
        ports = re.findall(listen_pattern, content)
        analysis['ports'].extend([int(p) for p in ports])
    
    # Look for environment variables
    env_pattern = r'process\.env\.([A-Z_][A-Z0-9_]*)'
    env_vars = re.findall(env_pattern, content)
    analysis['environment_variables'] = env_vars
    
    return analysis


def analyze_go_code(content: str) -> Dict[str, Any]:
    """Analyze Go source code."""
    analysis = {
        'imports': [],
        'ports': [],
        'environment_variables': []
    }
    
    # Look for HTTP server
    if 'http.ListenAndServe' in content:
        listen_pattern = r'http\.ListenAndServe\s*\(\s*[\'"]:[^\'"]?(\d+)[\'"]'
        ports = re.findall(listen_pattern, content)
        analysis['ports'].extend([int(p) for p in ports])
    
    # Look for environment variables
    env_pattern = r'os\.Getenv\s*\(\s*[\'"]([A-Z_][A-Z0-9_]*)[\'"]'
    env_vars = re.findall(env_pattern, content)
    analysis['environment_variables'] = env_vars
    
    return analysis


def analyze_java_code(content: str) -> Dict[str, Any]:
    """Analyze Java source code."""
    analysis = {
        'imports': [],
        'ports': [],
        'environment_variables': []
    }
    
    # Look for Spring Boot
    if '@SpringBootApplication' in content or 'spring' in content.lower():
        analysis['framework'] = 'spring'
    
    # Look for server port configuration
    port_pattern = r'server\.port\s*=\s*(\d+)'
    ports = re.findall(port_pattern, content)
    analysis['ports'].extend([int(p) for p in ports])
    
    return analysis


def analyze_docker_file(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze Dockerfile for existing containerization setup."""
    analysis = {
        'base_image': None,
        'exposed_ports': [],
        'environment_variables': [],
        'volumes': [],
        'commands': []
    }
    
    lines = content.split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith('FROM'):
            analysis['base_image'] = line.split()[1] if len(line.split()) > 1 else None
        elif line.startswith('EXPOSE'):
            ports = re.findall(r'\d+', line)
            analysis['exposed_ports'].extend([int(p) for p in ports])
        elif line.startswith('ENV'):
            env_match = re.match(r'ENV\s+([A-Z_][A-Z0-9_]*)', line)
            if env_match:
                analysis['environment_variables'].append(env_match.group(1))
    
    return analysis


def analyze_documentation(file_path: str, content: str) -> Dict[str, Any]:
    """Analyze documentation files for setup instructions."""
    analysis = {
        'setup_instructions': [],
        'dependencies_mentioned': [],
        'ports_mentioned': []
    }
    
    # Look for port mentions
    port_pattern = r'(?:port|PORT)\s*(\d{4,5})'
    ports = re.findall(port_pattern, content)
    analysis['ports_mentioned'] = [int(p) for p in ports if 1000 <= int(p) <= 65535]
    
    # Look for common setup commands
    setup_patterns = [
        r'npm install',
        r'pip install',
        r'go mod download',
        r'cargo build',
        r'mvn install',
        r'gradle build'
    ]
    
    for pattern in setup_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            analysis['setup_instructions'].append(pattern)
    
    return analysis


if __name__ == "__main__":
    # Test the analyzer
    test_content = '''
import os
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello World!"

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port)
    '''
    
    result = analyze_file('app.py', test_content)
    print("Analysis result:")
    print(json.dumps(result, indent=2))
