"""
Enhancement nodes for the Repo Runner system - Gap Detection, Code Analysis, Auto-Generation, etc.
"""
import os
import json
import logging
from typing import Dict, List, Any, Optional
from pocketflow import Node, BatchNode

# Import utility functions
from utils.gap_detector import detect_gaps
from utils.code_analyzer import analyze_code_for_containerization
from utils.auto_generator import generate_missing_files
from utils.container_validator import validate_container_strategy
from utils.container_strategy import generate_container_strategy
from utils.call_llm import call_llm, call_llm_with_json_response


class GapDetectionNode(Node):
    """Identify missing files, configurations, and potential containerization issues."""
    
    def prep(self, shared):
        """Read current knowledge and file tree for gap analysis."""
        return {
            "current_knowledge": shared.get("current_knowledge", {}),
            "file_tree": shared.get("file_tree", []),
            "analyzed_files": shared.get("analyzed_files", {}),
            "repository_metadata": shared.get("repository_metadata", {})
        }
    
    def exec(self, prep_result):
        """Detect gaps in repository for containerization."""
        current_knowledge = prep_result["current_knowledge"]
        file_tree = prep_result["file_tree"]
        
        logging.info("Detecting gaps and missing components")
        
        # Detect gaps using utility function
        gaps = detect_gaps(current_knowledge, file_tree)
        
        # Use LLM for additional gap analysis
        llm_gaps = analyze_gaps_with_llm(current_knowledge, file_tree)
        
        # Merge gap analyses
        merged_gaps = merge_gap_analyses(gaps, llm_gaps)
        
        logging.info(f"Gap detection complete. Severity: {merged_gaps['severity']}")
        logging.info(f"Critical gaps: {len(merged_gaps.get('critical_gaps', []))}")
        
        return merged_gaps
    
    def post(self, shared, prep_res, exec_res):
        """Store gap analysis results."""
        shared["identified_gaps"] = exec_res
        
        # Determine if critical gaps exist
        critical_gaps = exec_res.get("critical_gaps", [])
        if critical_gaps:
            logging.warning(f"Critical gaps found: {critical_gaps}")
            return "critical_gaps_found"
        else:
            logging.info("No critical gaps found")
            return "gaps_analyzed"


class CodeAnalysisNode(Node):
    """Analyze source code for containerization-specific issues and requirements."""
    
    def prep(self, shared):
        """Read source files and current knowledge."""
        repo_path = shared.get("repo_path")
        current_knowledge = shared.get("current_knowledge", {})
        analyzed_files = shared.get("analyzed_files", {})
        
        if not repo_path:
            raise ValueError("Repository path not found")
        
        # Get source files from analyzed files
        source_files = {}
        language = (current_knowledge.get("language") or "").lower()
        
        for file_path, analysis in analyzed_files.items():
            if analysis.get("file_type") == "source_code":
                full_path = os.path.join(repo_path, file_path)
                try:
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                        source_files[file_path] = f.read()
                except Exception as e:
                    logging.warning(f"Could not read source file {file_path}: {e}")
        
        return {
            "source_files": source_files,
            "language": language,
            "current_knowledge": current_knowledge
        }
    
    def exec(self, prep_result):
        """Analyze source code for containerization requirements."""
        source_files = prep_result["source_files"]
        language = prep_result["language"]
        current_knowledge = prep_result["current_knowledge"]
        
        logging.info(f"Analyzing {len(source_files)} source files for containerization")
        
        if not source_files:
            logging.warning("No source files found for analysis")
            return {"code_analysis": {}, "issues_found": []}
        
        # Analyze code using utility function
        code_analysis = analyze_code_for_containerization(source_files, language)
        
        # Use LLM for additional code analysis
        llm_analysis = analyze_code_with_llm(source_files, language, current_knowledge)
        
        # Merge analyses
        merged_analysis = merge_code_analyses(code_analysis, llm_analysis)
        
        # Identify specific issues
        issues_found = identify_code_issues(merged_analysis, current_knowledge)
        
        logging.info(f"Code analysis complete. Found {len(issues_found)} issues")
        
        return {
            "code_analysis": merged_analysis,
            "issues_found": issues_found
        }
    
    def post(self, shared, prep_res, exec_res):
        """Update current knowledge with code analysis results."""
        current_knowledge = shared.get("current_knowledge", {})
        
        # Merge code analysis into current knowledge
        code_analysis = exec_res["code_analysis"]
        merge_code_analysis_into_knowledge(current_knowledge, code_analysis)
        
        shared["current_knowledge"] = current_knowledge
        shared["code_issues"] = exec_res["issues_found"]
        
        return "default"


class AutoGenerationNode(Node):
    """Generate missing files and create code modifications to fix containerization issues."""
    
    def prep(self, shared):
        """Read gaps and current knowledge for auto-generation."""
        return {
            "identified_gaps": shared.get("identified_gaps", {}),
            "current_knowledge": shared.get("current_knowledge", {}),
            "code_issues": shared.get("code_issues", []),
            "repository_metadata": shared.get("repository_metadata", {})
        }
    
    def exec(self, prep_result):
        """Generate missing files and code modifications."""
        identified_gaps = prep_result["identified_gaps"]
        current_knowledge = prep_result["current_knowledge"]
        code_issues = prep_result["code_issues"]
        repository_metadata = prep_result["repository_metadata"]
        
        logging.info("Generating missing files and code modifications")
        
        # Prepare template context
        template_context = prepare_template_context(current_knowledge, repository_metadata)
        
        # Generate missing files
        generated_files = generate_missing_files(identified_gaps, template_context)
        
        # Generate code modifications
        code_modifications = generate_code_modifications(code_issues, current_knowledge)
        
        # Use LLM for additional generation suggestions
        llm_suggestions = generate_with_llm(identified_gaps, current_knowledge, code_issues)
        
        logging.info(f"Generated {len(generated_files)} files and {len(code_modifications)} code modifications")
        
        return {
            "generated_files": generated_files,
            "code_modifications": code_modifications,
            "llm_suggestions": llm_suggestions
        }
    
    def post(self, shared, prep_res, exec_res):
        """Store generated files and modifications."""
        shared["generated_files"] = exec_res["generated_files"]
        shared["code_modifications"] = exec_res["code_modifications"]
        shared["generation_suggestions"] = exec_res["llm_suggestions"]
        
        return "default"


class ContainerStrategyNode(Node):
    """Generate comprehensive container deployment strategy with all components."""
    
    def prep(self, shared):
        """Read complete knowledge and generated components."""
        return {
            "current_knowledge": shared.get("current_knowledge", {}),
            "generated_files": shared.get("generated_files", {}),
            "code_modifications": shared.get("code_modifications", {}),
            "repository_metadata": shared.get("repository_metadata", {})
        }
    
    def exec(self, prep_result):
        """Generate comprehensive container strategy."""
        current_knowledge = prep_result["current_knowledge"]
        generated_files = prep_result["generated_files"]
        repository_metadata = prep_result["repository_metadata"]
        
        logging.info("Generating comprehensive container strategy")
        
        # Enhance knowledge with generated files info
        enhanced_knowledge = enhance_knowledge_with_generated_files(
            current_knowledge, generated_files, repository_metadata
        )
        
        # Generate container strategy
        container_strategy = generate_container_strategy(enhanced_knowledge)
        
        # Use LLM for strategy validation and enhancement
        llm_enhancements = enhance_strategy_with_llm(container_strategy, enhanced_knowledge)
        
        # Merge LLM enhancements
        final_strategy = merge_strategy_enhancements(container_strategy, llm_enhancements)
        
        logging.info("Container strategy generation complete")
        
        return final_strategy
    
    def post(self, shared, prep_res, exec_res):
        """Store comprehensive container strategy."""
        shared["container_strategy"] = exec_res
        
        return "default"


class ValidationTestingNode(Node):
    """Validate the container strategy and test actual container build."""
    
    def prep(self, shared):
        """Read container strategy for validation."""
        return {
            "container_strategy": shared.get("container_strategy", {}),
            "current_knowledge": shared.get("current_knowledge", {}),
            "generated_files": shared.get("generated_files", {})
        }
    
    def exec(self, prep_result):
        """Validate container strategy and test build."""
        container_strategy = prep_result["container_strategy"]
        current_knowledge = prep_result["current_knowledge"]
        
        logging.info("Validating container strategy")
        
        dockerfile = container_strategy.get("dockerfile", "")
        docker_compose = container_strategy.get("docker_compose", "")
        
        if not dockerfile:
            logging.error("No Dockerfile found in container strategy")
            return {
                "validation_results": {"overall_status": "failed", "issues": ["No Dockerfile generated"]},
                "test_results": {},
                "recommendations": ["Generate Dockerfile before validation"]
            }
        
        # Validate container strategy
        validation_results = validate_container_strategy(dockerfile, docker_compose)
        
        # Additional validation with LLM
        llm_validation = validate_with_llm(container_strategy, current_knowledge)
        
        # Generate recommendations
        recommendations = generate_validation_recommendations(validation_results, llm_validation)
        
        logging.info(f"Validation complete. Status: {validation_results.get('overall_status', 'unknown')}")
        
        return {
            "validation_results": validation_results,
            "llm_validation": llm_validation,
            "recommendations": recommendations
        }
    
    def post(self, shared, prep_res, exec_res):
        """Store validation results."""
        shared["validation_results"] = exec_res["validation_results"]
        shared["llm_validation"] = exec_res["llm_validation"]
        shared["validation_recommendations"] = exec_res["recommendations"]
        
        # Determine if validation passed
        overall_status = exec_res["validation_results"].get("overall_status", "failed")
        if overall_status in ["passed", "partial"]:
            return "validation_passed"
        else:
            return "validation_failed"


class OptimizationNode(Node):
    """Suggest improvements and optimizations for the container setup."""
    
    def prep(self, shared):
        """Read all results for optimization analysis."""
        return {
            "container_strategy": shared.get("container_strategy", {}),
            "validation_results": shared.get("validation_results", {}),
            "current_knowledge": shared.get("current_knowledge", {}),
            "confidence_score": shared.get("confidence_score", 0.0)
        }
    
    def exec(self, prep_result):
        """Generate optimization suggestions."""
        container_strategy = prep_result["container_strategy"]
        validation_results = prep_result["validation_results"]
        current_knowledge = prep_result["current_knowledge"]
        confidence_score = prep_result["confidence_score"]
        
        logging.info("Generating optimization suggestions")
        
        # Generate optimization suggestions
        optimizations = generate_optimization_suggestions(
            container_strategy, validation_results, current_knowledge
        )
        
        # Use LLM for additional optimization ideas
        llm_optimizations = optimize_with_llm(container_strategy, current_knowledge, validation_results)
        
        # Calculate final confidence score
        final_confidence = calculate_final_confidence(
            confidence_score, validation_results, optimizations
        )
        
        logging.info(f"Optimization complete. Final confidence: {final_confidence:.2f}")
        
        return {
            "optimization_suggestions": optimizations,
            "llm_optimizations": llm_optimizations,
            "final_confidence_score": final_confidence
        }
    
    def post(self, shared, prep_res, exec_res):
        """Store optimization results and update confidence."""
        shared["optimization_suggestions"] = exec_res["optimization_suggestions"]
        shared["llm_optimizations"] = exec_res["llm_optimizations"]
        shared["confidence_score"] = exec_res["final_confidence_score"]
        
        return "default"


# Helper functions for enhancement nodes

def analyze_gaps_with_llm(current_knowledge: Dict[str, Any], file_tree: List[Dict]) -> Dict[str, Any]:
    """Use LLM to analyze additional gaps."""
    prompt = f"""
    Analyze this repository for containerization gaps:

    Current Knowledge: {json.dumps(current_knowledge, indent=2)}
    Total Files: {len(file_tree)}

    Identify missing components for containerization:
    1. Missing dependency files
    2. Missing configuration files
    3. Missing Docker files
    4. Security issues
    5. Code issues that affect containerization

    Respond with JSON:
    {{
        "missing_files": [list of missing files],
        "security_issues": [list of security concerns],
        "code_issues": [list of code problems],
        "recommendations": [list of recommendations]
    }}
    """

    try:
        return call_llm_with_json_response(prompt)
    except Exception as e:
        logging.warning(f"LLM gap analysis failed: {e}")
        return {"missing_files": [], "security_issues": [], "code_issues": [], "recommendations": []}


def merge_gap_analyses(gaps: Dict[str, Any], llm_gaps: Dict[str, Any]) -> Dict[str, Any]:
    """Merge gap analyses from utility and LLM."""
    merged = gaps.copy()

    # Merge missing files
    if llm_gaps.get("missing_files"):
        merged["missing_files"].extend(llm_gaps["missing_files"])

    # Merge security issues
    if llm_gaps.get("security_issues"):
        merged["security_issues"].extend(llm_gaps["security_issues"])

    # Merge code issues
    if llm_gaps.get("code_issues"):
        merged["code_issues"].extend(llm_gaps["code_issues"])

    return merged


def analyze_code_with_llm(source_files: Dict[str, str], language: str,
                         current_knowledge: Dict[str, Any]) -> Dict[str, Any]:
    """Use LLM for additional code analysis."""
    # Limit content for LLM
    limited_files = {}
    for path, content in list(source_files.items())[:5]:  # Limit to 5 files
        limited_files[path] = content[:2000]  # Limit content length

    prompt = f"""
    Analyze this {language} code for containerization requirements:

    Files: {json.dumps(limited_files, indent=2)}
    Current Knowledge: {json.dumps(current_knowledge, indent=2)}

    Extract:
    1. Additional ports not yet detected
    2. Environment variables used in code
    3. Database connections and types
    4. External service dependencies
    5. File paths that need to be configurable
    6. Security issues (hardcoded secrets, etc.)

    Respond with JSON:
    {{
        "ports": [list of port numbers],
        "environment_variables": {{"var": "default_value"}},
        "database_connections": [list of db info],
        "external_services": [list of services],
        "file_paths": [list of paths],
        "security_issues": [list of issues]
    }}
    """

    try:
        return call_llm_with_json_response(prompt)
    except Exception as e:
        logging.warning(f"LLM code analysis failed: {e}")
        return {"ports": [], "environment_variables": {}, "database_connections": [],
                "external_services": [], "file_paths": [], "security_issues": []}


def merge_code_analyses(code_analysis: Dict[str, Any], llm_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Merge code analyses from utility and LLM."""
    merged = code_analysis.copy()

    # Merge ports
    if llm_analysis.get("ports"):
        merged["ports"].extend(llm_analysis["ports"])
        merged["ports"] = list(set(merged["ports"]))

    # Merge environment variables
    if llm_analysis.get("environment_variables"):
        merged["environment_variables"].update(llm_analysis["environment_variables"])

    # Merge database connections
    if llm_analysis.get("database_connections"):
        merged["database_connections"].extend(llm_analysis["database_connections"])

    # Merge external services
    if llm_analysis.get("external_services"):
        merged["external_services"].extend(llm_analysis["external_services"])

    return merged


def identify_code_issues(code_analysis: Dict[str, Any], current_knowledge: Dict[str, Any]) -> List[Dict[str, str]]:
    """Identify specific code issues that affect containerization."""
    issues = []

    # Check for hardcoded ports
    ports = code_analysis.get("ports", [])
    env_vars = code_analysis.get("environment_variables", {})

    if ports and not any("port" in (var or "").lower() for var in env_vars.keys()):
        issues.append({
            "type": "hardcoded_ports",
            "description": f"Hardcoded ports found: {ports}",
            "suggestion": "Make ports configurable via environment variables",
            "severity": "medium"
        })

    # Check for hardcoded file paths
    file_paths = code_analysis.get("file_paths", [])
    absolute_paths = [path for path in file_paths if path.startswith('/')]

    if absolute_paths:
        issues.append({
            "type": "absolute_paths",
            "description": f"Absolute file paths found: {absolute_paths[:3]}",
            "suggestion": "Use relative paths or environment variables",
            "severity": "high"
        })

    # Check for security issues
    security_issues = code_analysis.get("security_issues", [])
    if security_issues:
        issues.append({
            "type": "security_vulnerabilities",
            "description": f"Security issues detected: {len(security_issues)}",
            "suggestion": "Review and fix security vulnerabilities",
            "severity": "critical"
        })

    return issues


def merge_code_analysis_into_knowledge(current_knowledge: Dict[str, Any], code_analysis: Dict[str, Any]):
    """Merge code analysis results into current knowledge."""
    # Merge ports
    if code_analysis.get("ports"):
        current_ports = current_knowledge.get("detected_ports", [])
        current_knowledge["detected_ports"] = list(set(current_ports + code_analysis["ports"]))

    # Merge environment variables
    if code_analysis.get("environment_variables"):
        current_env = current_knowledge.get("environment_variables", {})
        current_env.update(code_analysis["environment_variables"])
        current_knowledge["environment_variables"] = current_env

    # Merge database requirements
    if code_analysis.get("database_connections"):
        current_db = current_knowledge.get("database_requirements", [])
        current_knowledge["database_requirements"] = current_db + code_analysis["database_connections"]

    # Merge external services
    if code_analysis.get("external_services"):
        current_services = current_knowledge.get("external_services", [])
        current_knowledge["external_services"] = current_services + code_analysis["external_services"]


def prepare_template_context(current_knowledge: Dict[str, Any], repository_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare context for file generation templates."""
    return {
        "language": current_knowledge.get("language", ""),
        "framework": current_knowledge.get("framework", ""),
        "dependencies": current_knowledge.get("dependencies", []),
        "detected_ports": current_knowledge.get("detected_ports", [8080]),
        "environment_variables": current_knowledge.get("environment_variables", {}),
        "database_connections": current_knowledge.get("database_requirements", []),
        "repository_name": repository_metadata.get("name", "app"),
        "description": repository_metadata.get("description", ""),
        "main_entry_point": current_knowledge.get("entry_points", ["main"])[0] if current_knowledge.get("entry_points") else "main"
    }


def generate_code_modifications(code_issues: List[Dict], current_knowledge: Dict[str, Any]) -> Dict[str, Any]:
    """Generate code modifications to fix identified issues."""
    modifications = {}

    for issue in code_issues:
        issue_type = issue.get("type")

        if issue_type == "hardcoded_ports":
            modifications["port_configuration"] = {
                "description": "Make ports configurable",
                "changes": [
                    "Replace hardcoded ports with environment variables",
                    "Add PORT environment variable with default value",
                    "Update server startup code to use configurable port"
                ]
            }

        elif issue_type == "absolute_paths":
            modifications["path_configuration"] = {
                "description": "Fix absolute file paths",
                "changes": [
                    "Convert absolute paths to relative paths",
                    "Use environment variables for configurable paths",
                    "Add path validation and error handling"
                ]
            }

        elif issue_type == "security_vulnerabilities":
            modifications["security_fixes"] = {
                "description": "Address security vulnerabilities",
                "changes": [
                    "Move hardcoded secrets to environment variables",
                    "Add input validation and sanitization",
                    "Implement proper error handling"
                ]
            }

    return modifications


def generate_with_llm(identified_gaps: Dict[str, Any], current_knowledge: Dict[str, Any],
                     code_issues: List[Dict]) -> Dict[str, Any]:
    """Use LLM for additional generation suggestions."""
    prompt = f"""
    Based on the analysis, suggest additional files and modifications needed:

    Identified Gaps: {json.dumps(identified_gaps, indent=2)}
    Current Knowledge: {json.dumps(current_knowledge, indent=2)}
    Code Issues: {json.dumps(code_issues, indent=2)}

    Suggest:
    1. Additional files that should be generated
    2. Code modifications to improve containerization
    3. Configuration improvements
    4. Security enhancements

    Respond with JSON:
    {{
        "additional_files": [list of files to generate],
        "code_modifications": [list of modifications],
        "configuration_improvements": [list of improvements],
        "security_enhancements": [list of enhancements]
    }}
    """

    try:
        return call_llm_with_json_response(prompt)
    except Exception as e:
        logging.warning(f"LLM generation suggestions failed: {e}")
        return {"additional_files": [], "code_modifications": [],
                "configuration_improvements": [], "security_enhancements": []}


def enhance_knowledge_with_generated_files(current_knowledge: Dict[str, Any],
                                         generated_files: Dict[str, str],
                                         repository_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Enhance knowledge with information from generated files."""
    enhanced = current_knowledge.copy()

    # Add repository name if not present
    if not enhanced.get("repository_name"):
        enhanced["repository_name"] = repository_metadata.get("name", "app")

    # If we generated files, we have more confidence in our analysis
    if generated_files:
        enhanced["has_generated_files"] = True
        enhanced["generated_file_count"] = len(generated_files)

    return enhanced


def enhance_strategy_with_llm(container_strategy: Dict[str, Any],
                            enhanced_knowledge: Dict[str, Any]) -> Dict[str, Any]:
    """Use LLM to enhance container strategy."""
    prompt = f"""
    Review and enhance this container strategy:

    Current Strategy: {json.dumps(container_strategy, indent=2)}
    Knowledge: {json.dumps(enhanced_knowledge, indent=2)}

    Suggest improvements for:
    1. Dockerfile optimization
    2. Docker-compose enhancements
    3. Security improvements
    4. Performance optimizations
    5. Best practices

    Respond with JSON:
    {{
        "dockerfile_improvements": [list of improvements],
        "compose_improvements": [list of improvements],
        "security_improvements": [list of improvements],
        "performance_optimizations": [list of optimizations],
        "best_practices": [list of practices]
    }}
    """

    try:
        return call_llm_with_json_response(prompt)
    except Exception as e:
        logging.warning(f"LLM strategy enhancement failed: {e}")
        return {"dockerfile_improvements": [], "compose_improvements": [],
                "security_improvements": [], "performance_optimizations": [], "best_practices": []}


def merge_strategy_enhancements(container_strategy: Dict[str, Any],
                               llm_enhancements: Dict[str, Any]) -> Dict[str, Any]:
    """Merge LLM enhancements into container strategy."""
    enhanced_strategy = container_strategy.copy()

    # Add enhancement suggestions to the strategy
    enhanced_strategy["enhancement_suggestions"] = llm_enhancements

    return enhanced_strategy


def validate_with_llm(container_strategy: Dict[str, Any],
                     current_knowledge: Dict[str, Any]) -> Dict[str, Any]:
    """Use LLM for additional validation."""
    dockerfile = container_strategy.get("dockerfile", "")[:2000]  # Limit length

    prompt = f"""
    Validate this containerization strategy:

    Dockerfile (excerpt): {dockerfile}
    Knowledge: {json.dumps(current_knowledge, indent=2)}

    Check for:
    1. Missing dependencies
    2. Security vulnerabilities
    3. Performance issues
    4. Best practice violations
    5. Potential runtime errors

    Respond with JSON:
    {{
        "validation_status": "passed|failed|warning",
        "issues_found": [list of issues],
        "recommendations": [list of recommendations],
        "confidence": 0.0-1.0
    }}
    """

    try:
        return call_llm_with_json_response(prompt)
    except Exception as e:
        logging.warning(f"LLM validation failed: {e}")
        return {"validation_status": "warning", "issues_found": [], "recommendations": [], "confidence": 0.5}


def generate_validation_recommendations(validation_results: Dict[str, Any],
                                      llm_validation: Dict[str, Any]) -> List[str]:
    """Generate validation recommendations."""
    recommendations = []

    # Add recommendations from validation results
    if validation_results.get("suggestions"):
        recommendations.extend(validation_results["suggestions"])

    # Add recommendations from LLM validation
    if llm_validation.get("recommendations"):
        recommendations.extend(llm_validation["recommendations"])

    # Add general recommendations based on issues
    if validation_results.get("issues"):
        recommendations.append("Address validation issues before deployment")

    if not validation_results.get("build_successful"):
        recommendations.append("Fix build issues and test locally")

    return list(set(recommendations))  # Remove duplicates


def generate_optimization_suggestions(container_strategy: Dict[str, Any],
                                    validation_results: Dict[str, Any],
                                    current_knowledge: Dict[str, Any]) -> List[str]:
    """Generate optimization suggestions."""
    suggestions = []

    # Add suggestions from container strategy
    if container_strategy.get("optimization_suggestions"):
        suggestions.extend(container_strategy["optimization_suggestions"])

    # Add suggestions based on validation results
    if validation_results.get("build_time", 0) > 120:  # More than 2 minutes
        suggestions.append("Consider using multi-stage builds to reduce build time")

    if validation_results.get("image_size"):
        suggestions.append("Consider using Alpine Linux base images for smaller size")

    # Add language-specific suggestions
    language = (current_knowledge.get("language") or "").lower()
    if language == "python":
        suggestions.append("Use pip install --no-cache-dir to reduce image size")
        suggestions.append("Consider using Python slim images")
    elif language in ["javascript", "typescript"]:
        suggestions.append("Use npm ci instead of npm install for faster builds")
        suggestions.append("Consider using node:alpine for smaller images")

    return suggestions


def optimize_with_llm(container_strategy: Dict[str, Any],
                     current_knowledge: Dict[str, Any],
                     validation_results: Dict[str, Any]) -> Dict[str, Any]:
    """Use LLM for optimization suggestions."""
    prompt = f"""
    Suggest optimizations for this containerization setup:

    Strategy: {json.dumps(container_strategy, indent=2)}
    Knowledge: {json.dumps(current_knowledge, indent=2)}
    Validation: {json.dumps(validation_results, indent=2)}

    Suggest optimizations for:
    1. Build performance
    2. Image size reduction
    3. Runtime performance
    4. Security hardening
    5. Resource efficiency

    Respond with JSON:
    {{
        "build_optimizations": [list of build improvements],
        "size_optimizations": [list of size reductions],
        "runtime_optimizations": [list of runtime improvements],
        "security_hardening": [list of security improvements],
        "resource_efficiency": [list of resource optimizations]
    }}
    """

    try:
        return call_llm_with_json_response(prompt)
    except Exception as e:
        logging.warning(f"LLM optimization failed: {e}")
        return {"build_optimizations": [], "size_optimizations": [],
                "runtime_optimizations": [], "security_hardening": [], "resource_efficiency": []}


def calculate_final_confidence(base_confidence: float,
                             validation_results: Dict[str, Any],
                             optimizations: List[str]) -> float:
    """Calculate final confidence score."""
    confidence = base_confidence

    # Adjust based on validation results
    overall_status = validation_results.get("overall_status", "failed")
    if overall_status == "passed":
        confidence += 0.1
    elif overall_status == "partial":
        confidence += 0.05
    elif overall_status == "failed":
        confidence -= 0.1

    # Adjust based on build success
    if validation_results.get("build_successful"):
        confidence += 0.05
    else:
        confidence -= 0.05

    # Adjust based on container startup
    if validation_results.get("container_starts"):
        confidence += 0.05

    # Adjust based on optimizations available
    if len(optimizations) > 5:
        confidence += 0.02  # Many optimizations available means room for improvement

    return min(max(confidence, 0.0), 1.0)  # Clamp between 0 and 1
