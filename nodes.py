"""
Node implementations for the Repo Runner system.
"""
import os
import json
import logging
from typing import Dict, List, Any, Optional
from pocketflow import Node, BatchNode

# Import utility functions
from utils.github_client import clone_repository, get_repository_metadata, get_repository_languages, build_file_tree, cleanup_repository
from utils.file_analyzer import analyze_file
from utils.gap_detector import detect_gaps
from utils.code_analyzer import analyze_code_for_containerization
from utils.auto_generator import generate_missing_files
from utils.container_validator import validate_container_strategy
from utils.priority_scorer import prioritize_files, get_next_batch_for_analysis
from utils.container_strategy import generate_container_strategy
from utils.call_llm import call_llm, call_llm_with_json_response


class RepositoryInitializationNode(Node):
    """Initialize repository analysis by cloning and scanning file structure."""

    def prep(self, shared):
        """Read repository URL from shared store."""
        repository_url = shared.get("repository_url")
        if not repository_url:
            raise ValueError("Repository URL is required")

        access_token = shared.get("access_token")  # Optional
        return {"repository_url": repository_url, "access_token": access_token}

    def exec(self, prep_result):
        """Clone repository and perform initial analysis."""
        repository_url = prep_result["repository_url"]
        access_token = prep_result["access_token"]

        logging.info(f"Initializing analysis for repository: {repository_url}")

        try:
            # Get repository metadata
            metadata = get_repository_metadata(repository_url, access_token)
            logging.info(f"Repository: {metadata['full_name']} ({metadata['language']})")

            # Get programming languages
            languages = get_repository_languages(repository_url, access_token)

            # Clone repository
            repo_path = clone_repository(repository_url)
            logging.info(f"Repository cloned to: {repo_path}")

            # Build file tree
            file_tree = build_file_tree(repo_path)
            logging.info(f"Found {len(file_tree)} files and directories")

            # Identify high-priority files for initial analysis
            critical_files = identify_critical_files(file_tree)

            return {
                "repository_metadata": metadata,
                "languages": languages,
                "repo_path": repo_path,
                "file_tree": file_tree,
                "critical_files": critical_files,
                "total_files": len([f for f in file_tree if f['type'] == 'file'])
            }

        except Exception as e:
            logging.error(f"Repository initialization failed: {e}")
            raise

    def post(self, shared, prep_res, exec_res):
        """Store initialization results in shared store."""
        shared["repository_metadata"] = exec_res["repository_metadata"]
        shared["languages"] = exec_res["languages"]
        shared["repo_path"] = exec_res["repo_path"]
        shared["file_tree"] = exec_res["file_tree"]
        shared["total_files"] = exec_res["total_files"]

        # Initialize current knowledge with basic information
        language = exec_res["repository_metadata"].get("language") or ""
        shared["current_knowledge"]["language"] = language.lower() if language else ""

        # Set initial files to analyze
        shared["files_to_analyze"] = exec_res["critical_files"]

        logging.info(f"Repository initialization complete. {len(exec_res['critical_files'])} critical files identified.")

        return "default"


class FilePriorityAgentNode(Node):
    """Intelligently prioritize which files to read next based on current knowledge gaps."""

    def prep(self, shared):
        """Read current state for priority analysis."""
        return {
            "current_knowledge": shared.get("current_knowledge", {}),
            "files_to_analyze": shared.get("files_to_analyze", []),
            "analyzed_files": shared.get("analyzed_files", {}),
            "file_tree": shared.get("file_tree", []),
            "current_iteration": shared.get("current_iteration", 0),
            "max_iterations": shared.get("max_iterations", 50)
        }

    def exec(self, prep_result):
        """Determine next files to analyze based on knowledge gaps."""
        current_knowledge = prep_result["current_knowledge"]
        file_tree = prep_result["file_tree"]
        analyzed_files = prep_result["analyzed_files"]
        current_iteration = prep_result["current_iteration"]
        max_iterations = prep_result["max_iterations"]

        logging.info(f"Priority analysis - Iteration {current_iteration}/{max_iterations}")

        # Filter out already analyzed files
        unanalyzed_files = [
            f for f in file_tree
            if f['type'] == 'file' and f['path'] not in analyzed_files
        ]

        if not unanalyzed_files:
            logging.info("No more files to analyze")
            return {
                "next_files": [],
                "analysis_complete": True,
                "knowledge_gaps": {"critical_gaps": [], "minor_gaps": [], "priority_areas": []},
                "remaining_files": 0
            }

        # Use LLM to assess current knowledge gaps
        knowledge_gaps = assess_knowledge_gaps_with_llm(current_knowledge)

        # Prioritize files based on current knowledge and gaps
        prioritized_files = prioritize_files(unanalyzed_files, current_knowledge)

        # Select next batch for analysis
        batch_size = determine_batch_size(current_knowledge, len(unanalyzed_files))
        next_files = get_next_batch_for_analysis(prioritized_files, batch_size)

        logging.info(f"Selected {len(next_files)} files for next analysis batch")

        return {
            "next_files": next_files,
            "knowledge_gaps": knowledge_gaps,
            "remaining_files": len(unanalyzed_files) - len(next_files),
            "analysis_complete": False
        }

    def post(self, shared, prep_res, exec_res):
        """Update files to analyze and iteration counter."""
        shared["files_to_analyze"] = exec_res["next_files"]
        shared["knowledge_gaps"] = exec_res["knowledge_gaps"]
        shared["current_iteration"] += 1

        if exec_res["analysis_complete"] or shared["current_iteration"] >= shared["max_iterations"]:
            logging.info("File analysis phase complete")
            return "analysis_complete"
        elif exec_res["next_files"]:
            return "continue_analysis"
        else:
            logging.warning("No files selected for analysis")
            return "analysis_complete"


class FileAnalysisNode(BatchNode):
    """Analyze selected files and extract relevant information for containerization."""

    def prep(self, shared):
        """Get files to analyze from shared store."""
        files_to_analyze = shared.get("files_to_analyze", [])
        repo_path = shared.get("repo_path")

        if not repo_path:
            raise ValueError("Repository path not found")

        # Read file contents
        file_contents = []
        for file_info in files_to_analyze:
            file_path = os.path.join(repo_path, file_info['path'])
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                file_contents.append({
                    "file_info": file_info,
                    "content": content,
                    "full_path": file_path
                })
            except Exception as e:
                logging.warning(f"Could not read file {file_info['path']}: {e}")
                continue

        return file_contents

    def exec(self, file_data):
        """Analyze a single file."""
        file_info = file_data["file_info"]
        content = file_data["content"]

        logging.info(f"Analyzing file: {file_info['path']}")

        try:
            # Analyze file content
            analysis = analyze_file(file_info['path'], content)

            # Add file size and other metadata
            analysis.update({
                "file_size": len(content),
                "line_count": len(content.split('\n')),
                "analysis_timestamp": __import__('datetime').datetime.now().isoformat()
            })

            return {
                "file_path": file_info['path'],
                "analysis": analysis,
                "success": True
            }

        except Exception as e:
            logging.error(f"Failed to analyze file {file_info['path']}: {e}")
            return {
                "file_path": file_info['path'],
                "analysis": {},
                "success": False,
                "error": str(e)
            }

    def post(self, shared, prep_res, exec_res_list):
        """Update analyzed files and current knowledge."""
        analyzed_files = shared.get("analyzed_files", {})
        current_knowledge = shared.get("current_knowledge", {})

        successful_analyses = 0

        for result in exec_res_list:
            if result["success"]:
                file_path = result["file_path"]
                analysis = result["analysis"]

                # Store analysis
                analyzed_files[file_path] = analysis

                # Update current knowledge
                update_current_knowledge(current_knowledge, analysis)

                successful_analyses += 1
            else:
                logging.warning(f"Analysis failed for {result['file_path']}: {result.get('error', 'Unknown error')}")

        shared["analyzed_files"] = analyzed_files
        shared["current_knowledge"] = current_knowledge

        logging.info(f"Analyzed {successful_analyses}/{len(exec_res_list)} files successfully")

        return "default"


class KnowledgeSynthesisNode(Node):
    """Combine all findings and assess completeness of current knowledge."""

    def prep(self, shared):
        """Read all current knowledge and analysis results."""
        return {
            "current_knowledge": shared.get("current_knowledge", {}),
            "analyzed_files": shared.get("analyzed_files", {}),
            "file_tree": shared.get("file_tree", []),
            "repository_metadata": shared.get("repository_metadata", {}),
            "current_iteration": shared.get("current_iteration", 0)
        }

    def exec(self, prep_result):
        """Synthesize knowledge and assess completeness."""
        current_knowledge = prep_result["current_knowledge"]
        analyzed_files = prep_result["analyzed_files"]
        repository_metadata = prep_result["repository_metadata"]

        logging.info("Synthesizing knowledge from analyzed files")

        # Use LLM to synthesize findings
        synthesis_prompt = create_synthesis_prompt(current_knowledge, analyzed_files, repository_metadata)
        synthesis_result = call_llm(synthesis_prompt)

        # Parse LLM response to extract structured information
        synthesized_knowledge = parse_synthesis_result(synthesis_result, current_knowledge)

        # Assess completeness
        completeness_assessment = assess_completeness(synthesized_knowledge)

        # Calculate confidence score
        confidence_score = calculate_confidence_score(synthesized_knowledge, analyzed_files)

        logging.info(f"Knowledge synthesis complete. Confidence: {confidence_score:.2f}")

        return {
            "synthesized_knowledge": synthesized_knowledge,
            "completeness_assessment": completeness_assessment,
            "confidence_score": confidence_score,
            "synthesis_summary": synthesis_result
        }

    def post(self, shared, prep_res, exec_res):
        """Update current knowledge with synthesis results."""
        shared["current_knowledge"] = exec_res["synthesized_knowledge"]
        shared["completeness_assessment"] = exec_res["completeness_assessment"]
        shared["confidence_score"] = exec_res["confidence_score"]

        # Determine if we have sufficient information
        if exec_res["confidence_score"] >= 0.7 and exec_res["completeness_assessment"]["sufficient_info"]:
            logging.info("Sufficient information gathered for containerization")
            return "sufficient_info"
        elif shared.get("current_iteration", 0) >= shared.get("max_iterations", 50):
            logging.warning("Maximum iterations reached, proceeding with available information")
            return "max_iterations_reached"
        else:
            logging.info("Need more information, continuing analysis")
            return "need_more_info"


# Helper functions

def identify_critical_files(file_tree: List[Dict]) -> List[Dict]:
    """Identify critical files that should be analyzed first."""
    critical_patterns = [
        'package.json', 'requirements.txt', 'go.mod', 'cargo.toml', 'composer.json',
        'dockerfile', 'docker-compose.yml', 'docker-compose.yaml',
        'main.py', 'app.py', 'server.py', 'index.js', 'server.js', 'main.go', 'main.rs',
        'readme.md', 'readme.txt', 'readme.rst'
    ]

    critical_files = []
    for file_info in file_tree:
        if file_info['type'] == 'file':
            filename = file_info['name'].lower()
            if filename in critical_patterns or any(pattern in filename for pattern in ['config', 'settings']):
                critical_files.append(file_info)

    return critical_files


def assess_knowledge_gaps_with_llm(current_knowledge: Dict[str, Any]) -> Dict[str, Any]:
    """Use LLM to assess current knowledge gaps."""
    prompt = f"""
    Analyze the current knowledge about a repository and identify critical gaps for containerization:

    Current Knowledge:
    {json.dumps(current_knowledge, indent=2)}

    Identify what information is still missing for successful containerization. Focus on:
    1. Programming language and framework
    2. Dependencies and build requirements
    3. Application entry points
    4. Port configurations
    5. Environment variables
    6. Database requirements
    7. External service dependencies

    Respond with a JSON object containing:
    {{
        "critical_gaps": ["list of critical missing information"],
        "minor_gaps": ["list of minor missing information"],
        "priority_areas": ["areas that need immediate attention"]
    }}
    """

    try:
        return call_llm_with_json_response(prompt)
    except Exception as e:
        logging.warning(f"LLM knowledge gap assessment failed: {e}")
        return {"critical_gaps": [], "minor_gaps": [], "priority_areas": []}


def determine_batch_size(current_knowledge: Dict[str, Any], remaining_files: int) -> int:
    """Determine optimal batch size for file analysis."""
    # Start with larger batches, reduce as we gain more knowledge
    confidence = current_knowledge.get("confidence_score", 0.0)

    if confidence < 0.3:
        return min(10, remaining_files)  # Large batch for initial discovery
    elif confidence < 0.6:
        return min(5, remaining_files)   # Medium batch for targeted analysis
    else:
        return min(3, remaining_files)   # Small batch for final details


def update_current_knowledge(current_knowledge: Dict[str, Any], file_analysis: Dict[str, Any]):
    """Update current knowledge with new file analysis."""
    # Update language if not set
    if not current_knowledge.get("language") and file_analysis.get("language"):
        current_knowledge["language"] = file_analysis["language"]

    # Update framework if not set
    if not current_knowledge.get("framework") and file_analysis.get("framework"):
        current_knowledge["framework"] = file_analysis["framework"]

    # Merge dependencies
    if file_analysis.get("dependencies"):
        current_deps = current_knowledge.get("dependencies", [])
        current_knowledge["dependencies"] = list(set(current_deps + file_analysis["dependencies"]))

    # Merge ports
    if file_analysis.get("ports"):
        current_ports = current_knowledge.get("detected_ports", [])
        current_knowledge["detected_ports"] = list(set(current_ports + file_analysis["ports"]))

    # Merge environment variables
    if file_analysis.get("environment_variables"):
        current_env = current_knowledge.get("environment_variables", {})
        if isinstance(file_analysis["environment_variables"], list):
            for var in file_analysis["environment_variables"]:
                current_env[var] = None
        elif isinstance(file_analysis["environment_variables"], dict):
            current_env.update(file_analysis["environment_variables"])
        current_knowledge["environment_variables"] = current_env

    # Merge entry points
    if file_analysis.get("entry_points"):
        current_entry = current_knowledge.get("entry_points", [])
        current_knowledge["entry_points"] = list(set(current_entry + file_analysis["entry_points"]))

    # Merge database connections
    if file_analysis.get("database_connections"):
        current_db = current_knowledge.get("database_requirements", [])
        current_knowledge["database_requirements"] = current_db + file_analysis["database_connections"]


def create_synthesis_prompt(current_knowledge: Dict[str, Any], analyzed_files: Dict[str, Any],
                          repository_metadata: Dict[str, Any]) -> str:
    """Create prompt for LLM knowledge synthesis."""
    return f"""
    Synthesize the following information about a repository to create a comprehensive understanding for containerization:

    Repository Metadata:
    {json.dumps(repository_metadata, indent=2)}

    Current Knowledge:
    {json.dumps(current_knowledge, indent=2)}

    Number of analyzed files: {len(analyzed_files)}

    Based on this information, provide a comprehensive synthesis that includes:
    1. Confirmed programming language and framework
    2. Complete list of dependencies
    3. Application entry points and startup process
    4. Required ports and networking
    5. Environment variables and configuration
    6. Database and external service requirements
    7. Build and deployment requirements

    Focus on information needed for containerization. Be specific and actionable.
    Respond in a structured format that can be easily parsed.
    """


def parse_synthesis_result(synthesis_result: str, current_knowledge: Dict[str, Any]) -> Dict[str, Any]:
    """Parse LLM synthesis result and merge with current knowledge."""
    # This is a simplified parser - in practice, you'd want more robust parsing
    synthesized = current_knowledge.copy()

    # Try to extract structured information from the LLM response
    # For now, we'll just return the current knowledge with some enhancements
    # In a real implementation, you'd parse the LLM response more carefully

    return synthesized


def assess_completeness(knowledge: Dict[str, Any]) -> Dict[str, Any]:
    """Assess completeness of current knowledge."""
    required_fields = ["language", "framework", "dependencies", "entry_points"]
    optional_fields = ["detected_ports", "environment_variables", "database_requirements"]

    missing_required = [field for field in required_fields if not knowledge.get(field)]
    missing_optional = [field for field in optional_fields if not knowledge.get(field)]

    sufficient_info = len(missing_required) == 0
    completeness_score = (len(required_fields) - len(missing_required)) / len(required_fields)

    return {
        "sufficient_info": sufficient_info,
        "completeness_score": completeness_score,
        "missing_required": missing_required,
        "missing_optional": missing_optional
    }


def calculate_confidence_score(knowledge: Dict[str, Any], analyzed_files: Dict[str, Any]) -> float:
    """Calculate confidence score based on available information."""
    score = 0.0

    # Language detection (20%)
    if knowledge.get("language"):
        score += 0.2

    # Framework detection (20%)
    if knowledge.get("framework"):
        score += 0.2

    # Dependencies (15%)
    if knowledge.get("dependencies"):
        score += 0.15

    # Entry points (15%)
    if knowledge.get("entry_points"):
        score += 0.15

    # Configuration (10%)
    if knowledge.get("environment_variables") or knowledge.get("detected_ports"):
        score += 0.1

    # File coverage (20%)
    file_coverage = min(len(analyzed_files) / 20, 1.0)  # Assume 20 files is good coverage
    score += 0.2 * file_coverage

    return min(score, 1.0)