#!/usr/bin/env python3
"""
Test script to verify that all None value fixes are working.
"""
import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_none_handling():
    """Test that None values don't cause .lower() errors."""
    print("🧪 Testing None value handling...")
    
    # Test data with None values
    test_knowledge = {
        'language': None,
        'framework': None,
        'dependencies': [],
        'detected_ports': [],
        'environment_variables': {}
    }
    
    test_context = {
        'language': None,
        'framework': None,
        'dependencies': [],
        'database_connections': [{'type': None}]
    }
    
    test_file_info = {
        'name': 'test.py',
        'path': 'test.py',
        'type': 'file',
        'size': 100,
        'depth': 0
    }
    
    try:
        # Test priority scorer
        from utils.priority_scorer import calculate_file_priority_score
        score = calculate_file_priority_score(test_file_info, test_knowledge)
        print(f"✅ Priority scorer: {score}")
        
        # Test auto generator
        from utils.auto_generator import generate_dependency_files
        files = generate_dependency_files(test_context)
        print(f"✅ Auto generator: {len(files)} files")
        
        # Test container strategy
        from utils.container_strategy import generate_container_strategy
        strategy = generate_container_strategy(test_knowledge)
        print(f"✅ Container strategy: {len(strategy)} components")
        
        # Test enhancement nodes functions
        from enhancement_nodes import identify_code_issues
        issues = identify_code_issues({}, test_knowledge)
        print(f"✅ Code issues: {len(issues)} issues")
        
        print("✅ All None value handling tests passed!")
        return True
    
    except AttributeError as e:
        if "'NoneType' object has no attribute 'lower'" in str(e):
            print(f"❌ Still have None.lower() error: {e}")
            return False
        else:
            print(f"❌ Other AttributeError: {e}")
            return False
    except Exception as e:
        print(f"⚠️  Other error (may be expected): {e}")
        return True  # Other errors are OK for this test


def test_repository_initialization():
    """Test the repository initialization with None values."""
    print("\n🔧 Testing repository initialization...")
    
    try:
        from nodes import RepositoryInitializationNode
        
        # Create node
        node = RepositoryInitializationNode()
        
        # Mock shared data
        shared = {
            "repository_url": "https://github.com/test/repo",
            "current_knowledge": {
                "language": None,
                "framework": None,
                "dependencies": []
            }
        }
        
        # Test prep (this should not fail with None values)
        prep_result = node.prep(shared)
        print(f"✅ Repository init prep: {type(prep_result)}")
        
        return True
    
    except AttributeError as e:
        if "'NoneType' object has no attribute 'lower'" in str(e):
            print(f"❌ Repository init still has None.lower() error: {e}")
            return False
        else:
            print(f"⚠️  Other AttributeError in repo init: {e}")
            return True
    except Exception as e:
        print(f"⚠️  Other error in repo init (may be expected): {e}")
        return True


def test_file_analysis():
    """Test file analysis with None values."""
    print("\n📁 Testing file analysis...")
    
    try:
        from utils.file_analyzer import analyze_file
        
        # Test with simple Python code
        test_code = "print('Hello World')"
        result = analyze_file("test.py", test_code)
        
        # The result might have None values
        if result.get('language') is None:
            result['language'] = None
        if result.get('framework') is None:
            result['framework'] = None
        
        print(f"✅ File analysis: {result.get('language', 'None')}")
        
        # Test priority scoring with this result
        from utils.priority_scorer import calculate_file_priority_score
        
        test_file = {
            'name': 'test.py',
            'path': 'test.py',
            'type': 'file',
            'size': 100,
            'depth': 0
        }
        
        test_knowledge = {
            'language': result.get('language'),  # Might be None
            'framework': result.get('framework'),  # Might be None
            'dependencies': []
        }
        
        score = calculate_file_priority_score(test_file, test_knowledge)
        print(f"✅ Priority with None values: {score}")
        
        return True
    
    except AttributeError as e:
        if "'NoneType' object has no attribute 'lower'" in str(e):
            print(f"❌ File analysis still has None.lower() error: {e}")
            return False
        else:
            print(f"⚠️  Other AttributeError: {e}")
            return True
    except Exception as e:
        print(f"⚠️  Other error (may be expected): {e}")
        return True


def main():
    """Run all None value tests."""
    print("🔍 Testing None Value Fixes")
    print("=" * 40)
    
    tests = [
        ("None Handling", test_none_handling),
        ("Repository Init", test_repository_initialization),
        ("File Analysis", test_file_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*40}")
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All None value fixes are working!")
        print("\nYou can now run Repo Runner:")
        print("   python repo_runner_main.py https://github.com/user/repo")
    else:
        print("⚠️  Some tests failed. There may still be None.lower() issues.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
