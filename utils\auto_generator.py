"""
Auto generator for creating missing files and fixing containerization issues.
"""
import os
import json
from typing import Dict, List, Any, Optional
import re


def generate_missing_files(missing_components: Dict[str, Any], template_context: Dict[str, Any]) -> Dict[str, str]:
    """
    Generate missing files based on identified gaps.
    
    Args:
        missing_components: Dictionary of missing components from gap detection
        template_context: Context information for generating files
    
    Returns:
        Dictionary of filename -> file_content for generated files
    """
    generated_files = {}
    
    # Generate missing dependency files
    for missing_file in missing_components.get('missing_files', []):
        if missing_file.get('auto_generate'):
            file_type = missing_file['type']
            
            if file_type == 'dependency_file':
                generated_files.update(generate_dependency_files(template_context))
            elif file_type == 'environment_config':
                generated_files.update(generate_env_files(template_context))
            elif file_type == 'dockerfile':
                generated_files.update(generate_dockerfile(template_context))
            elif file_type == 'dockerignore':
                generated_files.update(generate_dockerignore(template_context))
            elif file_type == 'docker_compose':
                generated_files.update(generate_docker_compose(template_context))
    
    return generated_files


def generate_dependency_files(context: Dict[str, Any]) -> Dict[str, str]:
    """Generate dependency management files."""
    files = {}
    language = (context.get('language') or '').lower()
    dependencies = context.get('dependencies', [])

    if language == 'python':
        files['requirements.txt'] = generate_python_requirements(dependencies, context)
    elif language in ['javascript', 'typescript']:
        files['package.json'] = generate_package_json(dependencies, context)
    elif language == 'go':
        files['go.mod'] = generate_go_mod(context)
    elif language == 'rust':
        files['Cargo.toml'] = generate_cargo_toml(dependencies, context)
    
    return files


def generate_python_requirements(dependencies: List[str], context: Dict[str, Any]) -> str:
    """Generate requirements.txt for Python projects."""
    # Common version mappings for popular packages
    version_map = {
        'flask': '>=2.0.0',
        'django': '>=4.0.0',
        'fastapi': '>=0.68.0',
        'requests': '>=2.25.0',
        'psycopg2': '>=2.8.0',
        'pymongo': '>=3.12.0',
        'redis': '>=3.5.0',
        'celery': '>=5.0.0',
        'gunicorn': '>=20.0.0',
        'uvicorn': '>=0.15.0'
    }
    
    requirements = []
    
    # Add detected dependencies
    for dep in dependencies:
        if dep in version_map:
            requirements.append(f"{dep}{version_map[dep]}")
        else:
            requirements.append(dep)
    
    # Add common production dependencies based on framework
    framework = (context.get('framework') or '').lower()
    if framework == 'flask':
        if 'gunicorn' not in dependencies:
            requirements.append('gunicorn>=20.0.0')
    elif framework == 'fastapi':
        if 'uvicorn' not in dependencies:
            requirements.append('uvicorn[standard]>=0.15.0')
    
    # Add database drivers if database connections detected
    db_connections = context.get('database_connections', [])
    for db in db_connections:
        db_type = (db.get('type') or '').lower()
        if db_type == 'postgresql' and 'psycopg2' not in dependencies:
            requirements.append('psycopg2-binary>=2.8.0')
        elif db_type == 'mysql' and 'pymysql' not in dependencies:
            requirements.append('PyMySQL>=1.0.0')
        elif db_type == 'mongodb' and 'pymongo' not in dependencies:
            requirements.append('pymongo>=3.12.0')
        elif db_type == 'redis' and 'redis' not in dependencies:
            requirements.append('redis>=3.5.0')
    
    return '\n'.join(sorted(requirements)) + '\n'


def generate_package_json(dependencies: List[str], context: Dict[str, Any]) -> str:
    """Generate package.json for Node.js projects."""
    package_data = {
        "name": context.get('repository_name', 'app'),
        "version": "1.0.0",
        "description": context.get('description', ''),
        "main": context.get('main_entry_point', 'index.js'),
        "scripts": {
            "start": "node index.js",
            "dev": "nodemon index.js",
            "test": "echo \"Error: no test specified\" && exit 1"
        },
        "dependencies": {},
        "devDependencies": {
            "nodemon": "^2.0.0"
        },
        "engines": {
            "node": ">=14.0.0"
        }
    }
    
    # Add detected dependencies with common versions
    version_map = {
        'express': '^4.18.0',
        'mongoose': '^6.0.0',
        'redis': '^4.0.0',
        'cors': '^2.8.0',
        'helmet': '^5.0.0',
        'dotenv': '^16.0.0'
    }
    
    for dep in dependencies:
        package_data['dependencies'][dep] = version_map.get(dep, '^1.0.0')
    
    # Add framework-specific dependencies
    framework = (context.get('framework') or '').lower()
    if framework == 'express':
        package_data['dependencies']['express'] = '^4.18.0'
        package_data['dependencies']['cors'] = '^2.8.0'
        package_data['dependencies']['helmet'] = '^5.0.0'
    
    # Add environment variable support
    if context.get('environment_variables'):
        package_data['dependencies']['dotenv'] = '^16.0.0'
    
    return json.dumps(package_data, indent=2)


def generate_go_mod(context: Dict[str, Any]) -> str:
    """Generate go.mod for Go projects."""
    module_name = context.get('repository_name', 'app')
    go_version = "1.19"
    
    content = f"""module {module_name}

go {go_version}

require (
"""
    
    # Add common dependencies based on detected patterns
    dependencies = context.get('dependencies', [])
    if 'gin' in str(dependencies).lower():
        content += "    github.com/gin-gonic/gin v1.9.0\n"
    if 'postgres' in str(context.get('database_connections', [])).lower():
        content += "    github.com/lib/pq v1.10.0\n"
    
    content += ")\n"
    
    return content


def generate_cargo_toml(dependencies: List[str], context: Dict[str, Any]) -> str:
    """Generate Cargo.toml for Rust projects."""
    package_name = context.get('repository_name', 'app')
    
    content = f"""[package]
name = "{package_name}"
version = "0.1.0"
edition = "2021"

[dependencies]
"""
    
    # Add common dependencies
    version_map = {
        'actix-web': '4.0',
        'tokio': '1.0',
        'serde': '1.0',
        'serde_json': '1.0'
    }
    
    for dep in dependencies:
        version = version_map.get(dep, '1.0')
        content += f'{dep} = "{version}"\n'
    
    return content


def generate_env_files(context: Dict[str, Any]) -> Dict[str, str]:
    """Generate environment configuration files."""
    files = {}
    
    env_vars = context.get('environment_variables', {})
    if env_vars:
        env_content = "# Environment Variables\n"
        env_content += "# Copy this file to .env and fill in your values\n\n"
        
        for var, default_value in env_vars.items():
            if default_value is not None:
                env_content += f"{var}={default_value}\n"
            else:
                env_content += f"# {var}=your_value_here\n"
        
        # Add common environment variables
        if context.get('detected_ports'):
            port = context['detected_ports'][0]
            env_content += f"\n# Application port\nPORT={port}\n"
        
        if context.get('database_connections'):
            env_content += "\n# Database configuration\n"
            env_content += "# DATABASE_URL=your_database_url_here\n"
        
        files['.env.example'] = env_content
    
    return files


def generate_dockerfile(context: Dict[str, Any]) -> Dict[str, str]:
    """Generate Dockerfile based on language and framework."""
    language = (context.get('language') or '').lower()
    
    if language == 'python':
        dockerfile_content = generate_python_dockerfile(context)
    elif language in ['javascript', 'typescript']:
        dockerfile_content = generate_node_dockerfile(context)
    elif language == 'go':
        dockerfile_content = generate_go_dockerfile(context)
    elif language == 'rust':
        dockerfile_content = generate_rust_dockerfile(context)
    else:
        dockerfile_content = generate_generic_dockerfile(context)
    
    return {'Dockerfile': dockerfile_content}


def generate_python_dockerfile(context: Dict[str, Any]) -> str:
    """Generate Dockerfile for Python applications."""
    framework = (context.get('framework') or '').lower()
    port = context.get('detected_ports', [8000])[0]
    entry_point = context.get('main_entry_point', 'app.py')
    
    dockerfile = f"""# Use Python 3.11 slim image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app \\
    && chown -R app:app /app
USER app

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{port}/health || exit 1

# Run application
"""
    
    if framework == 'django':
        dockerfile += "CMD [\"python\", \"manage.py\", \"runserver\", \"0.0.0.0:8000\"]\n"
    elif framework == 'flask':
        dockerfile += f"CMD [\"python\", \"{entry_point}\"]\n"
    elif framework == 'fastapi':
        dockerfile += f"CMD [\"uvicorn\", \"{entry_point.replace('.py', '')}:app\", \"--host\", \"0.0.0.0\", \"--port\", \"{port}\"]\n"
    else:
        dockerfile += f"CMD [\"python\", \"{entry_point}\"]\n"
    
    return dockerfile


def generate_node_dockerfile(context: Dict[str, Any]) -> str:
    """Generate Dockerfile for Node.js applications."""
    port = context.get('detected_ports', [3000])[0]
    entry_point = context.get('main_entry_point', 'index.js')
    
    dockerfile = f"""# Use Node.js 18 Alpine image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
RUN chown -R nextjs:nodejs /app
USER nextjs

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{port}/health || exit 1

# Run application
CMD ["node", "{entry_point}"]
"""
    
    return dockerfile


def generate_go_dockerfile(context: Dict[str, Any]) -> str:
    """Generate Dockerfile for Go applications."""
    port = context.get('detected_ports', [8080])[0]
    
    dockerfile = f"""# Build stage
FROM golang:1.19-alpine AS builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates

WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/main .

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD wget --no-verbose --tries=1 --spider http://localhost:{port}/health || exit 1

# Run the binary
CMD ["./main"]
"""
    
    return dockerfile


def generate_rust_dockerfile(context: Dict[str, Any]) -> str:
    """Generate Dockerfile for Rust applications."""
    port = context.get('detected_ports', [8000])[0]
    
    dockerfile = f"""# Build stage
FROM rust:1.70 as builder

WORKDIR /app

# Copy manifests
COPY Cargo.toml Cargo.lock ./

# Copy source code
COPY src ./src

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    ca-certificates \\
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/target/release/app /app/

# Create non-root user
RUN useradd --create-home --shell /bin/bash app \\
    && chown -R app:app /app
USER app

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{port}/health || exit 1

# Run the binary
CMD ["./app"]
"""
    
    return dockerfile


def generate_generic_dockerfile(context: Dict[str, Any]) -> str:
    """Generate generic Dockerfile."""
    port = context.get('detected_ports', [8080])[0]
    
    dockerfile = f"""# Generic Dockerfile
FROM ubuntu:22.04

# Set working directory
WORKDIR /app

# Install basic dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy application code
COPY . .

# Expose port
EXPOSE {port}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{port}/health || exit 1

# Run application (customize this based on your needs)
CMD ["echo", "Please customize the CMD instruction for your application"]
"""
    
    return dockerfile


def generate_dockerignore(context: Dict[str, Any]) -> Dict[str, str]:
    """Generate .dockerignore file."""
    language = (context.get('language') or '').lower()
    
    dockerignore_content = """# Git
.git
.gitignore

# Documentation
README.md
*.md

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime
*.pid
*.seed
*.pid.lock

# Coverage
coverage/
.nyc_output

# Environment
.env
.env.local
.env.*.local

"""
    
    if language == 'python':
        dockerignore_content += """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.venv/
venv/
ENV/
env/
"""
    
    elif language in ['javascript', 'typescript']:
        dockerignore_content += """# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
"""
    
    elif language == 'go':
        dockerignore_content += """# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
vendor/
"""
    
    elif language == 'rust':
        dockerignore_content += """# Rust
target/
Cargo.lock
"""
    
    return {'.dockerignore': dockerignore_content}


def generate_docker_compose(context: Dict[str, Any]) -> Dict[str, str]:
    """Generate docker-compose.yml file."""
    app_name = context.get('repository_name', 'app')
    port = context.get('detected_ports', [8080])[0]
    
    compose_data = {
        'version': '3.8',
        'services': {
            app_name: {
                'build': '.',
                'ports': [f'{port}:{port}'],
                'environment': [],
                'depends_on': []
            }
        }
    }
    
    # Add database services if detected
    db_connections = context.get('database_connections', [])
    for db in db_connections:
        db_type = (db.get('type') or '').lower()
        
        if db_type == 'postgresql':
            compose_data['services']['postgres'] = {
                'image': 'postgres:15',
                'environment': [
                    'POSTGRES_DB=app',
                    'POSTGRES_USER=app',
                    'POSTGRES_PASSWORD=password'
                ],
                'volumes': ['postgres_data:/var/lib/postgresql/data'],
                'ports': ['5432:5432']
            }
            compose_data['services'][app_name]['depends_on'].append('postgres')
            compose_data['services'][app_name]['environment'].append('DATABASE_URL=***************************************/app')
        
        elif db_type == 'redis':
            compose_data['services']['redis'] = {
                'image': 'redis:7-alpine',
                'ports': ['6379:6379']
            }
            compose_data['services'][app_name]['depends_on'].append('redis')
            compose_data['services'][app_name]['environment'].append('REDIS_URL=redis://redis:6379')
    
    # Add volumes if databases are present
    if any(db.get('type') == 'postgresql' for db in db_connections):
        compose_data['volumes'] = {'postgres_data': {}}
    
    # Convert to YAML-like string (simplified)
    compose_content = f"""version: '{compose_data['version']}'

services:
"""
    
    for service_name, service_config in compose_data['services'].items():
        compose_content += f"  {service_name}:\n"
        
        for key, value in service_config.items():
            if isinstance(value, list):
                if value:  # Only add if list is not empty
                    compose_content += f"    {key}:\n"
                    for item in value:
                        compose_content += f"      - {item}\n"
            elif isinstance(value, dict):
                compose_content += f"    {key}:\n"
                for k, v in value.items():
                    compose_content += f"      {k}: {v}\n"
            else:
                compose_content += f"    {key}: {value}\n"
        compose_content += "\n"
    
    if 'volumes' in compose_data:
        compose_content += "volumes:\n"
        for volume_name in compose_data['volumes']:
            compose_content += f"  {volume_name}:\n"
    
    return {'docker-compose.yml': compose_content}


if __name__ == "__main__":
    # Test the auto generator
    test_context = {
        'language': 'python',
        'framework': 'flask',
        'dependencies': ['flask', 'requests'],
        'detected_ports': [5000],
        'environment_variables': {'DATABASE_URL': None, 'SECRET_KEY': None},
        'database_connections': [{'type': 'postgresql'}],
        'repository_name': 'test-app',
        'main_entry_point': 'app.py'
    }
    
    test_missing = {
        'missing_files': [
            {'type': 'dependency_file', 'auto_generate': True},
            {'type': 'dockerfile', 'auto_generate': True},
            {'type': 'environment_config', 'auto_generate': True}
        ]
    }
    
    generated = generate_missing_files(test_missing, test_context)
    
    print("Generated files:")
    for filename, content in generated.items():
        print(f"\n=== {filename} ===")
        print(content[:200] + "..." if len(content) > 200 else content)
