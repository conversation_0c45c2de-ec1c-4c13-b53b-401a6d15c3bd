#!/usr/bin/env python3
"""
Setup script for Repo Runner - helps users configure the environment.
"""
import os
import sys
import subprocess
import webbrowser
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")

    # Install python-dotenv first for .env file support
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "python-dotenv"
        ], check=True)
        print("✅ python-dotenv installed")
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Failed to install python-dotenv: {e}")

    requirements_file = Path("requirements_repo_runner.txt")
    if not requirements_file.exists():
        print("❌ requirements_repo_runner.txt not found")
        return False

    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def check_gemini_api_key():
    """Check if Gemini API key is configured."""
    api_key = os.environ.get("GEMINI_API_KEY")
    
    if api_key:
        print("✅ GEMINI_API_KEY is configured")
        return True
    else:
        print("⚠️  GEMINI_API_KEY is not configured")
        return False


def setup_gemini_api_key():
    """Help user set up Gemini API key."""
    print("\n🔑 Setting up Gemini API Key...")
    print("1. You need a Google Gemini API key to use Repo Runner")
    print("2. The API key is free for moderate usage")
    
    open_browser = input("\nOpen Google AI Studio to get API key? (y/N): ").strip().lower()
    if open_browser == 'y':
        webbrowser.open("https://makersuite.google.com/app/apikey")
        print("🌐 Opened https://makersuite.google.com/app/apikey in your browser")
    
    print("\nAfter getting your API key:")
    print("1. Copy the API key")
    print("2. Set it as an environment variable:")
    print("   - Linux/Mac: export GEMINI_API_KEY='your-api-key-here'")
    print("   - Windows: set GEMINI_API_KEY=your-api-key-here")
    print("   - Or add it to your shell profile (.bashrc, .zshrc, etc.)")
    
    api_key = input("\nPaste your API key here (or press Enter to skip): ").strip()
    
    if api_key:
        # Set for current session
        os.environ["GEMINI_API_KEY"] = api_key
        print("✅ API key set for current session")
        
        # Suggest permanent setup
        print("\n💡 To make this permanent, add this to your shell profile:")
        print(f"   export GEMINI_API_KEY='{api_key}'")
        
        return True
    else:
        print("⚠️  Skipped API key setup. You'll need to set GEMINI_API_KEY before using Repo Runner")
        return False


def check_github_token():
    """Check if GitHub token is configured (optional)."""
    token = os.environ.get("GITHUB_TOKEN")
    
    if token:
        print("✅ GITHUB_TOKEN is configured (optional)")
        return True
    else:
        print("ℹ️  GITHUB_TOKEN not configured (optional - for private repos and higher rate limits)")
        return False


def setup_github_token():
    """Help user set up GitHub token (optional)."""
    print("\n🐙 Setting up GitHub Token (Optional)...")
    print("GitHub token is optional but recommended for:")
    print("- Accessing private repositories")
    print("- Higher API rate limits")
    print("- Better reliability")
    
    setup_token = input("\nSet up GitHub token? (y/N): ").strip().lower()
    if setup_token != 'y':
        print("⏭️  Skipped GitHub token setup")
        return False
    
    print("\nTo get a GitHub token:")
    print("1. Go to GitHub Settings > Developer settings > Personal access tokens")
    print("2. Generate a new token (classic)")
    print("3. Select 'repo' scope for private repositories")
    print("4. Copy the token")
    
    open_browser = input("\nOpen GitHub token page? (y/N): ").strip().lower()
    if open_browser == 'y':
        webbrowser.open("https://github.com/settings/tokens")
        print("🌐 Opened GitHub token page in your browser")
    
    token = input("\nPaste your GitHub token here (or press Enter to skip): ").strip()
    
    if token:
        os.environ["GITHUB_TOKEN"] = token
        print("✅ GitHub token set for current session")
        print("\n💡 To make this permanent, add this to your shell profile:")
        print(f"   export GITHUB_TOKEN='{token}'")
        return True
    else:
        print("⚠️  Skipped GitHub token setup")
        return False


def test_installation():
    """Test the installation by running a simple test."""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        from utils.call_llm import call_llm
        from utils.file_analyzer import analyze_file
        print("✅ Core modules import successfully")
        
        # Test simple file analysis
        test_code = "print('Hello World')"
        result = analyze_file("test.py", test_code)
        if result.get("language") == "python":
            print("✅ File analysis working")
        else:
            print("⚠️  File analysis may have issues")
        
        # Test LLM call if API key is available
        if os.environ.get("GEMINI_API_KEY"):
            try:
                response = call_llm("Say 'test successful' if you can read this.")
                if response and len(response) > 0:
                    print("✅ Gemini API connection working")
                else:
                    print("⚠️  Gemini API response is empty")
            except Exception as e:
                print(f"⚠️  Gemini API test failed: {e}")
        else:
            print("⚠️  Cannot test Gemini API - no API key")
        
        return True
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Repo Runner Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed - could not install dependencies")
        sys.exit(1)
    
    # Check/setup Gemini API key
    if not check_gemini_api_key():
        if not setup_gemini_api_key():
            print("\n⚠️  Setup incomplete - Gemini API key not configured")
            print("   You can set it later with: export GEMINI_API_KEY='your-key'")
    
    # Check/setup GitHub token (optional)
    if not check_github_token():
        setup_github_token()
    
    # Test installation
    if test_installation():
        print("\n🎉 Setup completed successfully!")
        print("\nYou can now run Repo Runner:")
        print("   python repo_runner_main.py https://github.com/user/repo")
        print("\nOr try interactive mode:")
        print("   python repo_runner_main.py")
    else:
        print("\n⚠️  Setup completed with warnings")
        print("   Some features may not work correctly")
        print("   Check the error messages above")


if __name__ == "__main__":
    main()
