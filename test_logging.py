#!/usr/bin/env python3
"""
Test script for the conversation logging system.
"""
import os
import sys
import json
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.conversation_logger import ConversationLogger
from utils.call_llm import call_llm, call_llm_with_json_response


def test_conversation_logger():
    """Test the conversation logger functionality."""
    print("Testing Conversation Logger...")
    
    # Initialize logger
    logger = ConversationLogger(session_id="test_session")
    
    # Test repository logging
    logger.log_repository_info("https://github.com/test/repo", True)
    
    # Test LLM interaction logging
    logger.log_llm_interaction(
        node_name="TestNode",
        prompt="What is 2+2?",
        response="2+2 equals 4",
        model="gemini-1.5-flash",
        temperature=0.1,
        tokens_used=20,
        duration=1.5,
        success=True
    )
    
    # Test node execution logging
    logger.log_node_execution(
        node_name="TestNode",
        node_type="TestNodeType",
        input_data={"test": "input"},
        output_data={"test": "output"},
        duration=2.0,
        success=True
    )
    
    # Test flow transition logging
    logger.log_flow_transition(
        from_node="NodeA",
        to_node="NodeB", 
        action="continue",
        shared_state_keys=["key1", "key2"]
    )
    
    # Test warning and error logging
    logger.log_warning("This is a test warning", "TestNode")
    logger.log_error("This is a test error", "TestNode")
    
    # Finalize session
    test_results = {
        "test": "completed",
        "success": True,
        "files_generated": 3
    }
    logger.finalize_session(test_results)
    
    print(f"✅ Conversation logger test completed!")
    print(f"📁 Log files created in: {logger.log_dir}")
    print(f"📄 Session ID: {logger.session_id}")
    
    # List created files
    log_files = list(logger.log_dir.glob(f"{logger.session_id}*"))
    for log_file in log_files:
        print(f"   - {log_file.name} ({log_file.stat().st_size} bytes)")
    
    return True


def test_llm_logging():
    """Test LLM call logging."""
    print("\nTesting LLM Call Logging...")
    
    # Test simple LLM call
    response = call_llm("What is the capital of France?", node_name="TestLLMNode")
    print(f"✅ LLM Response: {response[:50]}...")
    
    # Test JSON LLM call
    json_prompt = """
    Respond with JSON containing information about Python:
    {
        "language": "Python",
        "type": "programming",
        "popular": true
    }
    """
    json_response = call_llm_with_json_response(json_prompt, node_name="TestJSONNode")
    print(f"✅ JSON Response: {json_response}")
    
    return True


def view_log_files():
    """View the contents of generated log files."""
    print("\n" + "="*60)
    print("LOG FILE CONTENTS")
    print("="*60)
    
    logs_dir = Path("logs")
    if not logs_dir.exists():
        print("No logs directory found.")
        return
    
    # Find the most recent session
    session_files = list(logs_dir.glob("*_summary.json"))
    if not session_files:
        print("No session files found.")
        return
    
    latest_session = max(session_files, key=lambda f: f.stat().st_mtime)
    session_id = latest_session.stem.replace("_summary", "")
    
    print(f"Viewing logs for session: {session_id}")
    
    # Show summary
    summary_file = logs_dir / f"{session_id}_summary.json"
    if summary_file.exists():
        print(f"\n📊 SUMMARY ({summary_file.name}):")
        with open(summary_file, 'r', encoding='utf-8') as f:
            summary = json.load(f)
            print(f"   Session ID: {summary.get('session_id')}")
            print(f"   Repository: {summary.get('repository_url', 'N/A')}")
            print(f"   Total LLM Calls: {summary.get('total_llm_calls', 0)}")
            print(f"   Total Tokens: {summary.get('total_tokens_used', 0)}")
            print(f"   Nodes Executed: {len(summary.get('nodes_executed', []))}")
            print(f"   Errors: {len(summary.get('errors', []))}")
            print(f"   Warnings: {len(summary.get('warnings', []))}")
    
    # Show conversation log (first few entries)
    conversation_file = logs_dir / f"{session_id}_conversation.jsonl"
    if conversation_file.exists():
        print(f"\n💬 CONVERSATION LOG ({conversation_file.name}) - First 3 entries:")
        with open(conversation_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 3:
                    break
                entry = json.loads(line)
                print(f"   {i+1}. {entry.get('event')} - {entry.get('node_name')} - {entry.get('timestamp')}")
                if entry.get('prompt'):
                    print(f"      Prompt: {entry['prompt'][:100]}...")
                if entry.get('response'):
                    print(f"      Response: {entry['response'][:100]}...")
    
    # Show flow log (first few entries)
    flow_file = logs_dir / f"{session_id}_flow.jsonl"
    if flow_file.exists():
        print(f"\n🔄 FLOW LOG ({flow_file.name}) - First 5 entries:")
        with open(flow_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 5:
                    break
                entry = json.loads(line)
                print(f"   {i+1}. {entry.get('event')} - {entry.get('timestamp')}")
                if entry.get('from_node') and entry.get('to_node'):
                    print(f"      {entry['from_node']} -> {entry['to_node']} ({entry.get('action')})")


if __name__ == "__main__":
    print("🧪 Testing Repo Runner Logging System")
    print("="*50)
    
    try:
        # Test conversation logger
        test_conversation_logger()
        
        # Test LLM logging
        test_llm_logging()
        
        # View log files
        view_log_files()
        
        print("\n✅ All tests completed successfully!")
        print("\n💡 You can now run Repo Runner and check the 'logs' directory for detailed logs.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
