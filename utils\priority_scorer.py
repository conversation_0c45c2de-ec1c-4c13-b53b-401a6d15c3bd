"""
Priority scorer for intelligently prioritizing which files to analyze next.
"""
import os
import re
from typing import Dict, List, Any, Tuple
from collections import defaultdict


def prioritize_files(file_list: List[Dict], current_knowledge: Dict[str, Any]) -> List[Dict]:
    """
    Prioritize files for analysis based on current knowledge and file characteristics.
    
    Args:
        file_list: List of file information dictionaries
        current_knowledge: Current understanding of the repository
    
    Returns:
        Sorted list of files by priority (highest first)
    """
    scored_files = []
    
    for file_info in file_list:
        if file_info['type'] == 'file':
            score = calculate_file_priority_score(file_info, current_knowledge)
            scored_files.append({
                **file_info,
                'priority_score': score,
                'priority_reasons': get_priority_reasons(file_info, current_knowledge, score)
            })
    
    # Sort by priority score (highest first)
    scored_files.sort(key=lambda x: x['priority_score'], reverse=True)
    
    return scored_files


def calculate_file_priority_score(file_info: Dict, current_knowledge: Dict[str, Any]) -> float:
    """Calculate priority score for a single file."""
    score = 0.0
    filename = file_info['name'].lower()
    file_path = file_info['path'].lower()
    file_extension = file_info.get('extension', '').lower()
    
    # Base scores for different file types
    score += get_base_file_type_score(filename, file_extension)
    
    # Knowledge gap scoring
    score += get_knowledge_gap_score(file_info, current_knowledge)
    
    # Context-aware scoring
    score += get_context_aware_score(file_info, current_knowledge)
    
    # File characteristics scoring
    score += get_file_characteristics_score(file_info)
    
    # Dependency and framework specific scoring
    score += get_framework_specific_score(file_info, current_knowledge)
    
    return score


def get_base_file_type_score(filename: str, extension: str) -> float:
    """Get base priority score based on file type."""
    
    # Critical configuration files (highest priority)
    critical_files = {
        'package.json': 100,
        'requirements.txt': 100,
        'pipfile': 95,
        'pyproject.toml': 95,
        'go.mod': 100,
        'cargo.toml': 100,
        'composer.json': 95,
        'gemfile': 95,
        'pom.xml': 95,
        'build.gradle': 95,
        'dockerfile': 90,
        'docker-compose.yml': 85,
        'docker-compose.yaml': 85,
    }
    
    if filename in critical_files:
        return critical_files[filename]
    
    # Entry point files
    entry_point_files = {
        'main.py': 80,
        'app.py': 80,
        'server.py': 75,
        'index.js': 80,
        'server.js': 80,
        'app.js': 75,
        'main.go': 80,
        'main.rs': 80,
        'main.java': 75,
        'application.java': 75,
        'manage.py': 70,  # Django
    }
    
    if filename in entry_point_files:
        return entry_point_files[filename]
    
    # Configuration files
    config_patterns = [
        (r'.*config.*', 60),
        (r'.*settings.*', 60),
        (r'\.env.*', 65),
        (r'.*\.conf$', 55),
        (r'.*\.ini$', 50),
        (r'.*\.yaml$', 45),
        (r'.*\.yml$', 45),
        (r'.*\.json$', 40),
        (r'.*\.toml$', 45),
    ]
    
    for pattern, score in config_patterns:
        if re.match(pattern, filename):
            return score
    
    # Build and deployment files
    build_files = {
        'makefile': 50,
        'dockerfile': 90,
        '.dockerignore': 30,
        'docker-entrypoint.sh': 40,
        'start.sh': 45,
        'run.sh': 45,
        'build.sh': 40,
    }
    
    if filename in build_files:
        return build_files[filename]
    
    # Documentation (lower priority but still useful)
    doc_files = {
        'readme.md': 35,
        'readme.txt': 30,
        'readme.rst': 30,
        'changelog.md': 15,
        'license': 10,
        'contributing.md': 10,
    }
    
    if filename in doc_files:
        return doc_files[filename]
    
    # Source code files by extension
    source_extensions = {
        '.py': 25,
        '.js': 25,
        '.ts': 25,
        '.go': 25,
        '.rs': 25,
        '.java': 20,
        '.cpp': 20,
        '.c': 20,
        '.cs': 20,
        '.php': 20,
        '.rb': 20,
    }
    
    if extension in source_extensions:
        return source_extensions[extension]
    
    # Default score for other files
    return 5


def get_knowledge_gap_score(file_info: Dict, current_knowledge: Dict[str, Any]) -> float:
    """Score based on what knowledge gaps this file might fill."""
    score = 0.0
    filename = file_info['name'].lower()
    file_path = file_info['path'].lower()
    
    # Language detection gap
    if not current_knowledge.get('language'):
        if any(ext in filename for ext in ['.py', '.js', '.go', '.rs', '.java']):
            score += 50
        elif filename in ['package.json', 'requirements.txt', 'go.mod', 'cargo.toml']:
            score += 60
    
    # Framework detection gap
    if not current_knowledge.get('framework'):
        framework_indicators = {
            'django': ['manage.py', 'settings.py', 'wsgi.py', 'asgi.py'],
            'flask': ['app.py', 'application.py'],
            'express': ['server.js', 'app.js', 'index.js'],
            'spring': ['application.java', 'application.properties'],
            'rails': ['gemfile', 'config.ru', 'application.rb'],
        }
        
        for framework, indicators in framework_indicators.items():
            if any(indicator in filename for indicator in indicators):
                score += 40
    
    # Build system gap
    if not current_knowledge.get('build_system'):
        build_indicators = ['makefile', 'build.sh', 'webpack.config.js', 'gulpfile.js']
        if any(indicator in filename for indicator in build_indicators):
            score += 30
    
    # Entry points gap
    if not current_knowledge.get('entry_points'):
        if any(pattern in filename for pattern in ['main', 'app', 'server', 'index']):
            score += 35
    
    # Dependencies gap
    if not current_knowledge.get('dependencies'):
        dep_files = ['package.json', 'requirements.txt', 'go.mod', 'cargo.toml', 'gemfile']
        if filename in dep_files:
            score += 45
    
    # Configuration gap
    if not current_knowledge.get('configuration_files'):
        if any(pattern in filename for pattern in ['config', 'settings', '.env']):
            score += 25
    
    return score


def get_context_aware_score(file_info: Dict, current_knowledge: Dict[str, Any]) -> float:
    """Score based on current context and what we've learned."""
    score = 0.0
    filename = file_info['name'].lower()
    file_path = file_info['path'].lower()
    
    language = (current_knowledge.get('language') or '').lower()
    framework = (current_knowledge.get('framework') or '').lower()
    
    # Language-specific file priorities
    if language == 'python':
        python_priorities = {
            'wsgi.py': 30,
            'asgi.py': 30,
            'settings.py': 35,
            'urls.py': 25,
            'models.py': 20,
            'views.py': 20,
            'serializers.py': 15,
        }
        if filename in python_priorities:
            score += python_priorities[filename]
    
    elif language in ['javascript', 'typescript']:
        js_priorities = {
            'package-lock.json': 15,
            'yarn.lock': 15,
            'webpack.config.js': 25,
            'babel.config.js': 20,
            'tsconfig.json': 25,
            'next.config.js': 25,
            'nuxt.config.js': 25,
        }
        if filename in js_priorities:
            score += js_priorities[filename]
    
    elif language == 'go':
        go_priorities = {
            'go.sum': 20,
            'main.go': 30,
            'server.go': 25,
            'handler.go': 20,
            'router.go': 20,
        }
        if filename in go_priorities:
            score += go_priorities[filename]
    
    # Framework-specific priorities
    if framework == 'django':
        if 'settings' in filename or 'urls.py' in filename:
            score += 25
        elif 'models.py' in filename or 'views.py' in filename:
            score += 15
    
    elif framework == 'flask':
        if 'app.py' in filename or 'application.py' in filename:
            score += 25
        elif 'config.py' in filename:
            score += 20
    
    elif framework == 'express':
        if 'routes' in file_path:
            score += 20
        elif 'middleware' in file_path:
            score += 15
    
    # Directory context
    if 'config' in file_path:
        score += 15
    elif 'src' in file_path and language in ['javascript', 'typescript', 'java']:
        score += 10
    elif 'lib' in file_path:
        score += 8
    
    return score


def get_file_characteristics_score(file_info: Dict) -> float:
    """Score based on file characteristics like size, location, etc."""
    score = 0.0
    
    # File size considerations
    size = file_info.get('size', 0)
    if size > 0:
        if size < 1000:  # Very small files might be config
            score += 5
        elif size < 10000:  # Medium files likely contain useful info
            score += 10
        elif size < 100000:  # Large files might be important
            score += 8
        else:  # Very large files might be generated or less important
            score += 2
    
    # Depth in directory structure
    depth = file_info.get('depth', 0)
    if depth == 0:  # Root level files are often important
        score += 15
    elif depth == 1:
        score += 10
    elif depth == 2:
        score += 5
    # Deeper files get no bonus
    
    return score


def get_framework_specific_score(file_info: Dict, current_knowledge: Dict[str, Any]) -> float:
    """Score based on framework-specific patterns."""
    score = 0.0
    filename = file_info['name'].lower()
    file_path = file_info['path'].lower()
    
    detected_ports = current_knowledge.get('detected_ports', [])
    env_vars = current_knowledge.get('environment_variables', {})
    
    # If we've detected ports but no server files, prioritize server files
    if detected_ports and not any('server' in f for f in current_knowledge.get('analyzed_files', {})):
        if 'server' in filename or 'app' in filename:
            score += 20
    
    # If we've detected env vars but no config files, prioritize config files
    if env_vars and not any('config' in f for f in current_knowledge.get('analyzed_files', {})):
        if 'config' in filename or 'settings' in filename:
            score += 20
    
    # Database-related files if we've detected database usage
    db_requirements = current_knowledge.get('database_requirements', [])
    if db_requirements:
        db_files = ['models.py', 'schema.sql', 'migrations', 'database.py', 'db.py']
        if any(db_file in filename for db_file in db_files):
            score += 15
    
    return score


def get_priority_reasons(file_info: Dict, current_knowledge: Dict[str, Any], score: float) -> List[str]:
    """Get human-readable reasons for the priority score."""
    reasons = []
    filename = file_info['name'].lower()
    
    # High priority reasons
    if score >= 80:
        if filename in ['package.json', 'requirements.txt', 'go.mod', 'cargo.toml']:
            reasons.append("Critical dependency file")
        elif filename in ['main.py', 'app.py', 'index.js', 'server.js']:
            reasons.append("Likely application entry point")
        elif filename == 'dockerfile':
            reasons.append("Existing containerization configuration")
    
    # Medium priority reasons
    elif score >= 40:
        if 'config' in filename or 'settings' in filename:
            reasons.append("Configuration file")
        elif '.env' in filename:
            reasons.append("Environment configuration")
        elif any(ext in filename for ext in ['.py', '.js', '.go', '.rs']):
            reasons.append("Source code file")
    
    # Knowledge gap reasons
    if not current_knowledge.get('language'):
        reasons.append("Needed for language detection")
    if not current_knowledge.get('framework'):
        reasons.append("Needed for framework detection")
    if not current_knowledge.get('dependencies'):
        reasons.append("Needed for dependency analysis")
    
    # Context reasons
    language = (current_knowledge.get('language') or '').lower()
    if language and language in filename:
        reasons.append(f"Relevant to detected {language} project")
    
    if not reasons:
        reasons.append("Standard file analysis")
    
    return reasons


def filter_files_by_priority_threshold(scored_files: List[Dict], threshold: float = 20.0) -> List[Dict]:
    """Filter files that meet minimum priority threshold."""
    return [f for f in scored_files if f['priority_score'] >= threshold]


def get_next_batch_for_analysis(scored_files: List[Dict], 
                               batch_size: int = 5, 
                               min_score: float = 15.0) -> List[Dict]:
    """Get the next batch of files for analysis."""
    # Filter by minimum score
    candidates = filter_files_by_priority_threshold(scored_files, min_score)
    
    # Return top N files
    return candidates[:batch_size]


if __name__ == "__main__":
    # Test the priority scorer
    test_files = [
        {'type': 'file', 'name': 'package.json', 'path': 'package.json', 'size': 1500, 'depth': 0},
        {'type': 'file', 'name': 'app.py', 'path': 'app.py', 'size': 3000, 'depth': 0},
        {'type': 'file', 'name': 'config.py', 'path': 'config/config.py', 'size': 800, 'depth': 1},
        {'type': 'file', 'name': 'utils.py', 'path': 'src/utils.py', 'size': 2000, 'depth': 1},
        {'type': 'file', 'name': 'README.md', 'path': 'README.md', 'size': 1200, 'depth': 0},
    ]
    
    test_knowledge = {
        'language': None,
        'framework': None,
        'dependencies': [],
        'analyzed_files': {}
    }
    
    prioritized = prioritize_files(test_files, test_knowledge)
    
    print("File Priority Analysis:")
    print("=" * 50)
    for file_info in prioritized:
        print(f"File: {file_info['name']}")
        print(f"Score: {file_info['priority_score']:.1f}")
        print(f"Reasons: {', '.join(file_info['priority_reasons'])}")
        print("-" * 30)
