#!/usr/bin/env python3
"""
Example script demonstrating Gemini integration with Repo Runner.
"""
import os
import sys
import json

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.call_llm import call_llm, call_llm_with_json_response


def test_basic_llm_call():
    """Test basic LLM functionality."""
    print("🧠 Testing basic Gemini LLM call...")
    
    prompt = """
    You are an expert in containerization and Docker. 
    Explain in 2-3 sentences what makes a good Dockerfile.
    """
    
    try:
        response = call_llm(prompt)
        print(f"✅ Response: {response}")
        return True
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def test_json_response():
    """Test JSON response functionality."""
    print("\n📋 Testing JSON response from Gemini...")
    
    prompt = """
    Analyze this simple Python Flask application and respond with JSON:
    
    ```python
    from flask import Flask
    import os
    
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return "Hello World!"
    
    @app.route('/health')
    def health():
        return "OK"
    
    if __name__ == '__main__':
        port = int(os.environ.get('PORT', 5000))
        app.run(host='0.0.0.0', port=port)
    ```
    
    Respond with JSON containing:
    {
        "language": "detected programming language",
        "framework": "detected web framework",
        "ports": [list of port numbers],
        "environment_variables": ["list of env vars used"],
        "endpoints": ["list of API endpoints"],
        "containerization_ready": true/false
    }
    """
    
    try:
        response = call_llm_with_json_response(prompt)
        print("✅ JSON Response:")
        print(json.dumps(response, indent=2))
        
        # Validate response structure
        expected_keys = ["language", "framework", "ports", "environment_variables", "endpoints"]
        missing_keys = [key for key in expected_keys if key not in response]
        
        if missing_keys:
            print(f"⚠️  Missing keys in response: {missing_keys}")
        else:
            print("✅ All expected keys present in JSON response")
        
        return True
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def test_containerization_analysis():
    """Test containerization-specific analysis."""
    print("\n🐳 Testing containerization analysis...")
    
    prompt = """
    You are a containerization expert. Analyze this repository structure and suggest what's missing for containerization:
    
    Repository files:
    - app.py (Flask application)
    - config.py (configuration file)
    - README.md (documentation)
    - static/ (static files directory)
    - templates/ (HTML templates)
    
    The app.py file contains:
    - Flask application with database connections
    - Environment variables: DATABASE_URL, SECRET_KEY, DEBUG
    - Runs on port from PORT environment variable (default 5000)
    - Uses PostgreSQL database
    
    Respond with JSON containing:
    {
        "missing_files": ["list of files needed for containerization"],
        "dockerfile_requirements": ["list of Dockerfile requirements"],
        "docker_compose_services": ["list of services needed"],
        "environment_variables": ["list of env vars to configure"],
        "security_considerations": ["list of security improvements"],
        "optimization_suggestions": ["list of optimization ideas"]
    }
    """
    
    try:
        response = call_llm_with_json_response(prompt)
        print("✅ Containerization Analysis:")
        print(json.dumps(response, indent=2))
        
        # Check if we got reasonable suggestions
        missing_files = response.get("missing_files", [])
        if any("dockerfile" in f.lower() for f in missing_files):
            print("✅ Correctly identified missing Dockerfile")
        
        if any("requirements" in f.lower() for f in missing_files):
            print("✅ Correctly identified missing requirements file")
        
        services = response.get("docker_compose_services", [])
        if any("postgres" in s.lower() for s in services):
            print("✅ Correctly identified PostgreSQL service need")
        
        return True
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def test_code_modification_suggestions():
    """Test code modification suggestions."""
    print("\n🔧 Testing code modification suggestions...")
    
    prompt = """
    Analyze this Python code and suggest modifications to make it more container-friendly:
    
    ```python
    import os
    from flask import Flask
    
    app = Flask(__name__)
    
    # Hardcoded configuration
    app.config['SECRET_KEY'] = 'my-secret-key-123'
    app.config['DATABASE_URL'] = 'postgresql://user:pass@localhost:5432/mydb'
    
    @app.route('/')
    def hello():
        # Hardcoded file path
        with open('/var/log/app.log', 'a') as f:
            f.write('Request received\\n')
        return "Hello World!"
    
    if __name__ == '__main__':
        # Hardcoded port and host
        app.run(host='127.0.0.1', port=8080, debug=True)
    ```
    
    Respond with JSON containing:
    {
        "issues_found": ["list of containerization issues"],
        "code_modifications": [
            {
                "issue": "description of issue",
                "current_code": "problematic code snippet",
                "suggested_code": "improved code snippet",
                "explanation": "why this change helps containerization"
            }
        ],
        "environment_variables_needed": ["list of env vars to add"],
        "dockerfile_considerations": ["list of Dockerfile considerations"]
    }
    """
    
    try:
        response = call_llm_with_json_response(prompt)
        print("✅ Code Modification Suggestions:")
        print(json.dumps(response, indent=2))
        
        # Check if we got reasonable suggestions
        issues = response.get("issues_found", [])
        if any("hardcoded" in issue.lower() for issue in issues):
            print("✅ Correctly identified hardcoded values")
        
        modifications = response.get("code_modifications", [])
        if len(modifications) > 0:
            print(f"✅ Provided {len(modifications)} code modification suggestions")
        
        return True
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def main():
    """Run all Gemini integration tests."""
    print("🚀 Gemini Integration Test for Repo Runner")
    print("=" * 60)
    
    # Check API key
    if not os.environ.get("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY environment variable not set")
        print("   Get your API key from: https://makersuite.google.com/app/apikey")
        print("   Then set it with: export GEMINI_API_KEY='your-api-key'")
        sys.exit(1)
    
    print("✅ GEMINI_API_KEY is configured")
    
    # Run tests
    tests = [
        ("Basic LLM Call", test_basic_llm_call),
        ("JSON Response", test_json_response),
        ("Containerization Analysis", test_containerization_analysis),
        ("Code Modification Suggestions", test_code_modification_suggestions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Gemini integration is working correctly.")
        print("\nYou can now run Repo Runner:")
        print("   python repo_runner_main.py https://github.com/user/repo")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        print("   Common issues:")
        print("   - Invalid API key")
        print("   - Network connectivity problems")
        print("   - API rate limits")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
