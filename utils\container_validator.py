"""
Container validator for testing and validating container configurations.
"""
import subprocess
import tempfile
import os
import time
import requests
from typing import Dict, List, Any, Optional, Tuple
import json


def validate_container_strategy(dockerfile: str, docker_compose: str = None) -> Dict[str, Any]:
    """
    Validate container strategy by testing builds and basic functionality.
    
    Args:
        dockerfile: Dockerfile content
        docker_compose: Optional docker-compose.yml content
    
    Returns:
        Validation results with success/failure status and details
    """
    validation_results = {
        'dockerfile_valid': False,
        'build_successful': False,
        'container_starts': False,
        'health_check_passes': False,
        'port_accessible': False,
        'issues': [],
        'warnings': [],
        'suggestions': [],
        'overall_status': 'failed'
    }
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Write Dockerfile
            dockerfile_path = os.path.join(temp_dir, 'Dockerfile')
            with open(dockerfile_path, 'w') as f:
                f.write(dockerfile)
            
            # Validate Dockerfile syntax
            validation_results.update(validate_dockerfile_syntax(dockerfile))
            
            # Write docker-compose if provided
            if docker_compose:
                compose_path = os.path.join(temp_dir, 'docker-compose.yml')
                with open(compose_path, 'w') as f:
                    f.write(docker_compose)
                validation_results.update(validate_docker_compose_syntax(docker_compose))
            
            # Test Docker build (if Docker is available)
            if is_docker_available():
                validation_results.update(test_docker_build(temp_dir))
                
                if validation_results['build_successful']:
                    validation_results.update(test_container_startup(temp_dir))
            else:
                validation_results['warnings'].append('Docker not available for build testing')
        
        except Exception as e:
            validation_results['issues'].append(f'Validation error: {str(e)}')
    
    # Determine overall status
    validation_results['overall_status'] = determine_validation_status(validation_results)
    
    return validation_results


def validate_dockerfile_syntax(dockerfile: str) -> Dict[str, Any]:
    """Validate Dockerfile syntax and best practices."""
    results = {
        'dockerfile_valid': True,
        'issues': [],
        'warnings': [],
        'suggestions': []
    }
    
    lines = dockerfile.split('\n')
    
    # Check for required instructions
    has_from = any(line.strip().upper().startswith('FROM') for line in lines)
    if not has_from:
        results['dockerfile_valid'] = False
        results['issues'].append('Missing FROM instruction')
    
    # Check for best practices
    has_workdir = any(line.strip().upper().startswith('WORKDIR') for line in lines)
    if not has_workdir:
        results['warnings'].append('Consider adding WORKDIR instruction')
    
    has_user = any(line.strip().upper().startswith('USER') for line in lines)
    if not has_user:
        results['warnings'].append('Consider running as non-root user')
    
    has_healthcheck = any(line.strip().upper().startswith('HEALTHCHECK') for line in lines)
    if not has_healthcheck:
        results['suggestions'].append('Consider adding HEALTHCHECK instruction')
    
    # Check for common issues
    for i, line in enumerate(lines, 1):
        line = line.strip()
        
        # Check for apt-get without cleanup
        if 'apt-get install' in line.lower() and 'rm -rf /var/lib/apt/lists/*' not in dockerfile:
            results['warnings'].append(f'Line {i}: Consider cleaning apt cache after installation')
        
        # Check for COPY before dependency installation
        if line.upper().startswith('COPY . .'):
            # Check if this comes before dependency installation
            remaining_lines = lines[i:]
            if any('install' in l.lower() for l in remaining_lines):
                results['suggestions'].append(f'Line {i}: Consider copying dependency files first for better caching')
    
    return results


def validate_docker_compose_syntax(docker_compose: str) -> Dict[str, Any]:
    """Validate docker-compose.yml syntax."""
    results = {
        'compose_valid': True,
        'issues': [],
        'warnings': [],
        'suggestions': []
    }
    
    try:
        import yaml
        compose_data = yaml.safe_load(docker_compose)
        
        # Check version
        if 'version' not in compose_data:
            results['warnings'].append('Missing version specification')
        
        # Check services
        if 'services' not in compose_data:
            results['compose_valid'] = False
            results['issues'].append('No services defined')
        else:
            services = compose_data['services']
            
            # Check each service
            for service_name, service_config in services.items():
                if not isinstance(service_config, dict):
                    results['issues'].append(f'Service {service_name} has invalid configuration')
                    continue
                
                # Check for image or build
                if 'image' not in service_config and 'build' not in service_config:
                    results['issues'].append(f'Service {service_name} missing image or build specification')
                
                # Check for exposed ports
                if 'ports' in service_config:
                    ports = service_config['ports']
                    if isinstance(ports, list):
                        for port in ports:
                            if isinstance(port, str) and ':' in port:
                                host_port, container_port = port.split(':')
                                if host_port == container_port:
                                    results['suggestions'].append(f'Service {service_name}: Consider using different host port')
    
    except yaml.YAMLError as e:
        results['compose_valid'] = False
        results['issues'].append(f'Invalid YAML syntax: {str(e)}')
    except Exception as e:
        results['issues'].append(f'Compose validation error: {str(e)}')
    
    return results


def is_docker_available() -> bool:
    """Check if Docker is available on the system."""
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False


def test_docker_build(build_context: str) -> Dict[str, Any]:
    """Test Docker build process."""
    results = {
        'build_successful': False,
        'build_time': 0,
        'image_size': 0,
        'issues': [],
        'warnings': []
    }
    
    try:
        start_time = time.time()
        
        # Build Docker image
        build_cmd = ['docker', 'build', '-t', 'repo-runner-test', build_context]
        result = subprocess.run(build_cmd, capture_output=True, text=True, timeout=300)
        
        build_time = time.time() - start_time
        results['build_time'] = build_time
        
        if result.returncode == 0:
            results['build_successful'] = True
            
            # Get image size
            size_cmd = ['docker', 'images', 'repo-runner-test', '--format', '{{.Size}}']
            size_result = subprocess.run(size_cmd, capture_output=True, text=True, timeout=30)
            if size_result.returncode == 0:
                results['image_size'] = size_result.stdout.strip()
            
            # Check for warnings in build output
            if 'warning' in result.stderr.lower():
                results['warnings'].append('Build completed with warnings')
        else:
            results['issues'].append(f'Build failed: {result.stderr}')
    
    except subprocess.TimeoutExpired:
        results['issues'].append('Build timed out after 5 minutes')
    except Exception as e:
        results['issues'].append(f'Build test error: {str(e)}')
    
    return results


def test_container_startup(build_context: str) -> Dict[str, Any]:
    """Test container startup and basic functionality."""
    results = {
        'container_starts': False,
        'startup_time': 0,
        'port_accessible': False,
        'health_check_passes': False,
        'issues': [],
        'warnings': []
    }
    
    container_id = None
    
    try:
        start_time = time.time()
        
        # Start container
        run_cmd = ['docker', 'run', '-d', '--name', 'repo-runner-test-container', 
                  '-p', '8080:8080', 'repo-runner-test']
        result = subprocess.run(run_cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            container_id = result.stdout.strip()
            results['container_starts'] = True
            
            # Wait for startup
            time.sleep(5)
            startup_time = time.time() - start_time
            results['startup_time'] = startup_time
            
            # Test port accessibility
            results['port_accessible'] = test_port_accessibility('localhost', 8080)
            
            # Test health check if available
            results['health_check_passes'] = test_health_check('localhost', 8080)
        else:
            results['issues'].append(f'Container failed to start: {result.stderr}')
    
    except subprocess.TimeoutExpired:
        results['issues'].append('Container startup timed out')
    except Exception as e:
        results['issues'].append(f'Container test error: {str(e)}')
    
    finally:
        # Cleanup container
        if container_id:
            cleanup_container(container_id)
    
    return results


def test_port_accessibility(host: str, port: int, timeout: int = 10) -> bool:
    """Test if a port is accessible."""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False


def test_health_check(host: str, port: int, timeout: int = 10) -> bool:
    """Test health check endpoint."""
    health_endpoints = ['/health', '/healthz', '/ping', '/status']
    
    for endpoint in health_endpoints:
        try:
            url = f'http://{host}:{port}{endpoint}'
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                return True
        except requests.RequestException:
            continue
    
    return False


def cleanup_container(container_id: str):
    """Clean up test container and image."""
    try:
        # Stop and remove container
        subprocess.run(['docker', 'stop', container_id], 
                      capture_output=True, timeout=30)
        subprocess.run(['docker', 'rm', container_id], 
                      capture_output=True, timeout=30)
        
        # Remove test image
        subprocess.run(['docker', 'rmi', 'repo-runner-test'], 
                      capture_output=True, timeout=30)
    except Exception:
        pass  # Ignore cleanup errors


def determine_validation_status(results: Dict[str, Any]) -> str:
    """Determine overall validation status."""
    if results.get('issues'):
        return 'failed'
    elif results.get('dockerfile_valid', False) and results.get('build_successful', False):
        if results.get('container_starts', False):
            return 'passed'
        else:
            return 'partial'
    elif results.get('dockerfile_valid', False):
        return 'syntax_only'
    else:
        return 'failed'


def generate_validation_report(results: Dict[str, Any]) -> str:
    """Generate a human-readable validation report."""
    report = "Container Validation Report\n"
    report += "=" * 30 + "\n\n"
    
    report += f"Overall Status: {results['overall_status'].upper()}\n\n"
    
    if results.get('dockerfile_valid'):
        report += "✓ Dockerfile syntax is valid\n"
    else:
        report += "✗ Dockerfile syntax issues found\n"
    
    if results.get('build_successful'):
        report += f"✓ Docker build successful (took {results.get('build_time', 0):.1f}s)\n"
        if results.get('image_size'):
            report += f"  Image size: {results['image_size']}\n"
    else:
        report += "✗ Docker build failed\n"
    
    if results.get('container_starts'):
        report += f"✓ Container starts successfully (took {results.get('startup_time', 0):.1f}s)\n"
    else:
        report += "✗ Container failed to start\n"
    
    if results.get('port_accessible'):
        report += "✓ Application port is accessible\n"
    else:
        report += "✗ Application port is not accessible\n"
    
    if results.get('health_check_passes'):
        report += "✓ Health check endpoint responds\n"
    else:
        report += "? Health check endpoint not found or not responding\n"
    
    # Issues
    if results.get('issues'):
        report += "\nIssues:\n"
        for issue in results['issues']:
            report += f"  • {issue}\n"
    
    # Warnings
    if results.get('warnings'):
        report += "\nWarnings:\n"
        for warning in results['warnings']:
            report += f"  • {warning}\n"
    
    # Suggestions
    if results.get('suggestions'):
        report += "\nSuggestions:\n"
        for suggestion in results['suggestions']:
            report += f"  • {suggestion}\n"
    
    return report


if __name__ == "__main__":
    # Test the validator
    test_dockerfile = """
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "app.py"]
"""
    
    results = validate_container_strategy(test_dockerfile)
    print(generate_validation_report(results))
